// Generated by nitro
import type { Serialize, Simplify } from "nitropack/types";
declare module "nitropack/types" {
  type Awaited<T> = T extends PromiseLike<infer U> ? Awaited<U> : T
  interface InternalApi {
    '/api/stories-content': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/stories-content.get').default>>>>
    }
    '/reelsitemap.xml': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/routes/reelsitemap.xml').default>>>>
    }
    '/robots.txt': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/routes/robots.txt').default>>>>
    }
    '/__nuxt_error': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/core/runtime/nitro/handlers/renderer').default>>>>
    }
    '/api/_nuxt_icon/:collection': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../../../node_modules/.pnpm/@nuxt+icon@1.14.0_vite@6.3.5_vue@3.5.17/node_modules/@nuxt/icon/dist/runtime/server/api').default>>>>
    }
    '/__nuxt_island/**': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/#internal/nuxt/island-renderer').default>>>>
    }
    '/_ipx/**': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../../../node_modules/.pnpm/@nuxt+image@1.10.0/node_modules/@nuxt/image/dist/runtime/ipx').default>>>>
    }
  }
}
export {}