// Generated by auto imports
export {}
declare global {
  const JSONParse: typeof import('../../utils/index')['JSONParse']
  const abortNavigation: typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/router')['abortNavigation']
  const acceptHMRUpdate: typeof import('../../../../node_modules/.pnpm/@pinia+nuxt@0.11.1_pinia@3.0.3/node_modules/@pinia/nuxt/dist/runtime/composables')['acceptHMRUpdate']
  const addRouteMiddleware: typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/router')['addRouteMiddleware']
  const callOnce: typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/once')['callOnce']
  const cancelIdleCallback: typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/compat/idle-callback')['cancelIdleCallback']
  const clearError: typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/error')['clearError']
  const clearMessageInstance: typeof import('../../composables/useMessage')['clearMessageInstance']
  const clearNuxtData: typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/asyncData')['clearNuxtData']
  const clearNuxtState: typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/state')['clearNuxtState']
  const computed: typeof import('vue')['computed']
  const createError: typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/error')['createError']
  const customRef: typeof import('vue')['customRef']
  const debounce: typeof import('../../utils/index')['debounce']
  const defineAppConfig: typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/nuxt')['defineAppConfig']
  const defineAsyncComponent: typeof import('vue')['defineAsyncComponent']
  const defineComponent: typeof import('vue')['defineComponent']
  const defineNuxtComponent: typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/component')['defineNuxtComponent']
  const defineNuxtLink: typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/components/nuxt-link')['defineNuxtLink']
  const defineNuxtPlugin: typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/nuxt')['defineNuxtPlugin']
  const defineNuxtRouteMiddleware: typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/router')['defineNuxtRouteMiddleware']
  const definePageMeta: typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/pages/runtime/composables')['definePageMeta']
  const definePayloadPlugin: typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/nuxt')['definePayloadPlugin']
  const definePayloadReducer: typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/payload')['definePayloadReducer']
  const definePayloadReviver: typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/payload')['definePayloadReviver']
  const defineStore: typeof import('../../../../node_modules/.pnpm/@pinia+nuxt@0.11.1_pinia@3.0.3/node_modules/@pinia/nuxt/dist/runtime/composables')['defineStore']
  const delay: typeof import('../../utils/index')['delay']
  const detectDeviceFromUserAgent: typeof import('../../composables/useDeviceDetection')['detectDeviceFromUserAgent']
  const effect: typeof import('vue')['effect']
  const effectScope: typeof import('vue')['effectScope']
  const formatTimestamp: typeof import('../../utils/index')['formatTimestamp']
  const generateUUID: typeof import('../../utils/index')['generateUUID']
  const getAppManifest: typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/manifest')['getAppManifest']
  const getCurrentInstance: typeof import('vue')['getCurrentInstance']
  const getCurrentScope: typeof import('vue')['getCurrentScope']
  const getRouteRules: typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/manifest')['getRouteRules']
  const h: typeof import('vue')['h']
  const hasInjectionContext: typeof import('vue')['hasInjectionContext']
  const inject: typeof import('vue')['inject']
  const injectHead: typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/head')['injectHead']
  const isAndroid: typeof import('../../utils/index')['isAndroid']
  const isIOS: typeof import('../../utils/index')['isIOS']
  const isMobile: typeof import('../../utils/index')['isMobile']
  const isNuxtError: typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/error')['isNuxtError']
  const isPrerendered: typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/payload')['isPrerendered']
  const isProxy: typeof import('vue')['isProxy']
  const isReactive: typeof import('vue')['isReactive']
  const isReadonly: typeof import('vue')['isReadonly']
  const isRef: typeof import('vue')['isRef']
  const isShallow: typeof import('vue')['isShallow']
  const isVue2: typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/compat/vue-demi')['isVue2']
  const isVue3: typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/compat/vue-demi')['isVue3']
  const loadPayload: typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/payload')['loadPayload']
  const markRaw: typeof import('vue')['markRaw']
  const mergeModels: typeof import('vue')['mergeModels']
  const message: typeof import('../../composables/useMessage')['message']
  const navigateTo: typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/router')['navigateTo']
  const nextTick: typeof import('vue')['nextTick']
  const onActivated: typeof import('vue')['onActivated']
  const onBeforeMount: typeof import('vue')['onBeforeMount']
  const onBeforeRouteLeave: typeof import('vue-router')['onBeforeRouteLeave']
  const onBeforeRouteUpdate: typeof import('vue-router')['onBeforeRouteUpdate']
  const onBeforeUnmount: typeof import('vue')['onBeforeUnmount']
  const onBeforeUpdate: typeof import('vue')['onBeforeUpdate']
  const onDeactivated: typeof import('vue')['onDeactivated']
  const onErrorCaptured: typeof import('vue')['onErrorCaptured']
  const onMounted: typeof import('vue')['onMounted']
  const onNuxtReady: typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/ready')['onNuxtReady']
  const onPrehydrate: typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/ssr')['onPrehydrate']
  const onRenderTracked: typeof import('vue')['onRenderTracked']
  const onRenderTriggered: typeof import('vue')['onRenderTriggered']
  const onScopeDispose: typeof import('vue')['onScopeDispose']
  const onServerPrefetch: typeof import('vue')['onServerPrefetch']
  const onUnmounted: typeof import('vue')['onUnmounted']
  const onUpdated: typeof import('vue')['onUpdated']
  const prefetchComponents: typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/preload')['prefetchComponents']
  const preloadComponents: typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/preload')['preloadComponents']
  const preloadPayload: typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/payload')['preloadPayload']
  const preloadRouteComponents: typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/preload')['preloadRouteComponents']
  const prerenderRoutes: typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/ssr')['prerenderRoutes']
  const provide: typeof import('vue')['provide']
  const proxyRefs: typeof import('vue')['proxyRefs']
  const reactive: typeof import('vue')['reactive']
  const readonly: typeof import('vue')['readonly']
  const ref: typeof import('vue')['ref']
  const refreshCookie: typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/cookie')['refreshCookie']
  const refreshNuxtData: typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/asyncData')['refreshNuxtData']
  const reloadNuxtApp: typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/chunk')['reloadNuxtApp']
  const requestIdleCallback: typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/compat/idle-callback')['requestIdleCallback']
  const resolveComponent: typeof import('vue')['resolveComponent']
  const setInterval: typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/compat/interval')['setInterval']
  const setMessageInstance: typeof import('../../composables/useMessage')['setMessageInstance']
  const setPageLayout: typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/router')['setPageLayout']
  const setResponseStatus: typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/ssr')['setResponseStatus']
  const shallowReactive: typeof import('vue')['shallowReactive']
  const shallowReadonly: typeof import('vue')['shallowReadonly']
  const shallowRef: typeof import('vue')['shallowRef']
  const showError: typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/error')['showError']
  const storeToRefs: typeof import('../../../../node_modules/.pnpm/@pinia+nuxt@0.11.1_pinia@3.0.3/node_modules/@pinia/nuxt/dist/runtime/composables')['storeToRefs']
  const throttle: typeof import('../../utils/index')['throttle']
  const toRaw: typeof import('vue')['toRaw']
  const toRef: typeof import('vue')['toRef']
  const toRefs: typeof import('vue')['toRefs']
  const toValue: typeof import('vue')['toValue']
  const triggerRef: typeof import('vue')['triggerRef']
  const tryUseNuxtApp: typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/nuxt')['tryUseNuxtApp']
  const unref: typeof import('vue')['unref']
  const updateAppConfig: typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/config')['updateAppConfig']
  const useAnalytics: typeof import('../../composables/useAnalytics')['useAnalytics']
  const useApi: typeof import('../../composables/useApi')['useApi']
  const useAppConfig: typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/config')['useAppConfig']
  const useAsyncData: typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/asyncData')['useAsyncData']
  const useAttrs: typeof import('vue')['useAttrs']
  const useBranding: typeof import('../../composables/useBranding')['useBranding']
  const useCdn: typeof import('../../composables/useCdn')['useCdn']
  const useCheckinStore: typeof import('../../stores/checkin')['useCheckinStore']
  const useCookie: typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/cookie')['useCookie']
  const useCssModule: typeof import('vue')['useCssModule']
  const useCssVars: typeof import('vue')['useCssVars']
  const useDataManager: typeof import('../../composables/useDataManager')['useDataManager']
  const useDeviceDetection: typeof import('../../composables/useDeviceDetection')['useDeviceDetection']
  const useError: typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/error')['useError']
  const useFetch: typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/fetch')['useFetch']
  const useFilterSeo: typeof import('../../composables/useFilterSeo')['useFilterSeo']
  const useGameStructuredData: typeof import('../../composables/useGameStructuredData')['useGameStructuredData']
  const useGtag: typeof import('../../../../node_modules/.pnpm/nuxt-gtag@3.0.3/node_modules/nuxt-gtag/dist/runtime/composables/useGtag')['useGtag']
  const useHead: typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/head')['useHead']
  const useHeadSafe: typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/head')['useHeadSafe']
  const useHydration: typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/hydrate')['useHydration']
  const useId: typeof import('vue')['useId']
  const useImage: typeof import('../../../../node_modules/.pnpm/@nuxt+image@1.10.0/node_modules/@nuxt/image/dist/runtime/composables')['useImage']
  const useLazyAsyncData: typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/asyncData')['useLazyAsyncData']
  const useLazyFetch: typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/fetch')['useLazyFetch']
  const useLink: typeof import('vue-router')['useLink']
  const useLoadingIndicator: typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/loading-indicator')['useLoadingIndicator']
  const useLoadingStore: typeof import('../../stores/loading')['useLoadingStore']
  const useMenuHeight: typeof import('../../composables/useMenuHeight')['useMenuHeight']
  const useMessage: typeof import('../../composables/useMessage')['useMessage']
  const useModel: typeof import('vue')['useModel']
  const useNuxtApp: typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/nuxt')['useNuxtApp']
  const useNuxtData: typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/asyncData')['useNuxtData']
  const useNuxtDevTools: typeof import('../../../../node_modules/.pnpm/@nuxt+devtools@2.6.2_vite@6.3.5_vue@3.5.17/node_modules/@nuxt/devtools/dist/runtime/use-nuxt-devtools')['useNuxtDevTools']
  const usePageSeo: typeof import('../../composables/usePageSeo')['usePageSeo']
  const usePaymentConfig: typeof import('../../composables/usePaymentConfig')['usePaymentConfig']
  const usePinia: typeof import('../../../../node_modules/.pnpm/@pinia+nuxt@0.11.1_pinia@3.0.3/node_modules/@pinia/nuxt/dist/runtime/composables')['usePinia']
  const usePreviewMode: typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/preview')['usePreviewMode']
  const useRechargeStore: typeof import('../../stores/recharge')['useRechargeStore']
  const useRequestEvent: typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/ssr')['useRequestEvent']
  const useRequestFetch: typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/ssr')['useRequestFetch']
  const useRequestHeader: typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/ssr')['useRequestHeader']
  const useRequestHeaders: typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/ssr')['useRequestHeaders']
  const useRequestURL: typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/url')['useRequestURL']
  const useResponseHeader: typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/ssr')['useResponseHeader']
  const useRoute: typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/router')['useRoute']
  const useRouteAnnouncer: typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/route-announcer')['useRouteAnnouncer']
  const useRouter: typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/router')['useRouter']
  const useRuntimeConfig: typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/nuxt')['useRuntimeConfig']
  const useRuntimeHook: typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/runtime-hook')['useRuntimeHook']
  const useScript: typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/script-stubs')['useScript']
  const useScriptClarity: typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/script-stubs')['useScriptClarity']
  const useScriptCloudflareWebAnalytics: typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/script-stubs')['useScriptCloudflareWebAnalytics']
  const useScriptCrisp: typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/script-stubs')['useScriptCrisp']
  const useScriptEventPage: typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/script-stubs')['useScriptEventPage']
  const useScriptFathomAnalytics: typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/script-stubs')['useScriptFathomAnalytics']
  const useScriptGoogleAdsense: typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/script-stubs')['useScriptGoogleAdsense']
  const useScriptGoogleAnalytics: typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/script-stubs')['useScriptGoogleAnalytics']
  const useScriptGoogleMaps: typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/script-stubs')['useScriptGoogleMaps']
  const useScriptGoogleTagManager: typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/script-stubs')['useScriptGoogleTagManager']
  const useScriptHotjar: typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/script-stubs')['useScriptHotjar']
  const useScriptIntercom: typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/script-stubs')['useScriptIntercom']
  const useScriptLemonSqueezy: typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/script-stubs')['useScriptLemonSqueezy']
  const useScriptMatomoAnalytics: typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/script-stubs')['useScriptMatomoAnalytics']
  const useScriptMetaPixel: typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/script-stubs')['useScriptMetaPixel']
  const useScriptNpm: typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/script-stubs')['useScriptNpm']
  const useScriptPlausibleAnalytics: typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/script-stubs')['useScriptPlausibleAnalytics']
  const useScriptRybbitAnalytics: typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/script-stubs')['useScriptRybbitAnalytics']
  const useScriptSegment: typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/script-stubs')['useScriptSegment']
  const useScriptSnapchatPixel: typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/script-stubs')['useScriptSnapchatPixel']
  const useScriptStripe: typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/script-stubs')['useScriptStripe']
  const useScriptTriggerConsent: typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/script-stubs')['useScriptTriggerConsent']
  const useScriptTriggerElement: typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/script-stubs')['useScriptTriggerElement']
  const useScriptUmamiAnalytics: typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/script-stubs')['useScriptUmamiAnalytics']
  const useScriptVimeoPlayer: typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/script-stubs')['useScriptVimeoPlayer']
  const useScriptXPixel: typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/script-stubs')['useScriptXPixel']
  const useScriptYouTubePlayer: typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/script-stubs')['useScriptYouTubePlayer']
  const useSeoMeta: typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/head')['useSeoMeta']
  const useServerHead: typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/head')['useServerHead']
  const useServerHeadSafe: typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/head')['useServerHeadSafe']
  const useServerSeoMeta: typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/head')['useServerSeoMeta']
  const useShadowRoot: typeof import('vue')['useShadowRoot']
  const useSlots: typeof import('vue')['useSlots']
  const useState: typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/state')['useState']
  const useStories: typeof import('../../composables/useStories')['useStories']
  const useStoriesEnhancer: typeof import('../../composables/useStoriesEnhancer')['useStoriesEnhancer']
  const useStoriesV2: typeof import('../../composables/useStoriesV2')['useStoriesV2']
  const useStoryStore: typeof import('../../stores/story')['useStoryStore']
  const useSysConfigStore: typeof import('../../stores/sysconfig')['useSysConfigStore']
  const useTagsFilter: typeof import('../../composables/useTagsFilter')['useTagsFilter']
  const useTasksStore: typeof import('../../stores/tasks')['useTasksStore']
  const useTemplateRef: typeof import('vue')['useTemplateRef']
  const useThemeStore: typeof import('../../stores/theme')['useThemeStore']
  const useTokenManager: typeof import('../../composables/useTokenManager')['useTokenManager']
  const useTrackEvent: typeof import('../../../../node_modules/.pnpm/nuxt-gtag@3.0.3/node_modules/nuxt-gtag/dist/runtime/composables/useTrackEvent')['useTrackEvent']
  const useTransitionState: typeof import('vue')['useTransitionState']
  const useUserStore: typeof import('../../stores/user')['useUserStore']
  const watch: typeof import('vue')['watch']
  const watchEffect: typeof import('vue')['watchEffect']
  const watchPostEffect: typeof import('vue')['watchPostEffect']
  const watchSyncEffect: typeof import('vue')['watchSyncEffect']
  const withCtx: typeof import('vue')['withCtx']
  const withDirectives: typeof import('vue')['withDirectives']
  const withKeys: typeof import('vue')['withKeys']
  const withMemo: typeof import('vue')['withMemo']
  const withModifiers: typeof import('vue')['withModifiers']
  const withScopeId: typeof import('vue')['withScopeId']
}
// for type re-export
declare global {
  // @ts-ignore
  export type { Component, ComponentPublicInstance, ComputedRef, DirectiveBinding, ExtractDefaultPropTypes, ExtractPropTypes, ExtractPublicPropTypes, InjectionKey, PropType, Ref, MaybeRef, MaybeRefOrGetter, VNode, WritableComputedRef } from 'vue'
  import('vue')
  // @ts-ignore
  export type { ApiResponse, Story, Actor, Category, SubCategory, StoriesResponse, StoryCategoriesResponse, CheckinResponse, ClaimCheckinResponse, TaskData, TasksResponse, TaskItem, CompleteTaskResponse, InviteCodeResponse, CheckInCoinsPerDay, SysConfigListResponse, PriceExtra, PriceItem, PriceListResponse, CheckoutCreateParams, CheckoutCreateResponse, ChatHistoryResponse } from '../../composables/useApi'
  import('../../composables/useApi')
  // @ts-ignore
  export type { BrandingConfig } from '../../composables/useBranding'
  import('../../composables/useBranding')
  // @ts-ignore
  export type { DataContext, StoriesData } from '../../composables/useDataManager'
  import('../../composables/useDataManager')
  // @ts-ignore
  export type { GameStructuredData } from '../../composables/useGameStructuredData'
  import('../../composables/useGameStructuredData')
  // @ts-ignore
  export type { MessageOptions, MessageType } from '../../composables/useMessage'
  import('../../composables/useMessage')
  // @ts-ignore
  export type { PageSeoConfig } from '../../composables/usePageSeo'
  import('../../composables/usePageSeo')
  // @ts-ignore
  export type { StoryContent, EnhancedStory, StoriesContentResponse } from '../../composables/useStoriesEnhancer'
  import('../../composables/useStoriesEnhancer')
  // @ts-ignore
  export type { UserType, TokenStatus, TokenUserInfo } from '../../composables/useTokenManager'
  import('../../composables/useTokenManager')
}
// for vue template auto import
import { UnwrapRef } from 'vue'
declare module 'vue' {
  interface ComponentCustomProperties {
    readonly JSONParse: UnwrapRef<typeof import('../../utils/index')['JSONParse']>
    readonly abortNavigation: UnwrapRef<typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/router')['abortNavigation']>
    readonly acceptHMRUpdate: UnwrapRef<typeof import('../../../../node_modules/.pnpm/@pinia+nuxt@0.11.1_pinia@3.0.3/node_modules/@pinia/nuxt/dist/runtime/composables')['acceptHMRUpdate']>
    readonly addRouteMiddleware: UnwrapRef<typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/router')['addRouteMiddleware']>
    readonly callOnce: UnwrapRef<typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/once')['callOnce']>
    readonly cancelIdleCallback: UnwrapRef<typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/compat/idle-callback')['cancelIdleCallback']>
    readonly clearError: UnwrapRef<typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/error')['clearError']>
    readonly clearMessageInstance: UnwrapRef<typeof import('../../composables/useMessage')['clearMessageInstance']>
    readonly clearNuxtData: UnwrapRef<typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/asyncData')['clearNuxtData']>
    readonly clearNuxtState: UnwrapRef<typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/state')['clearNuxtState']>
    readonly computed: UnwrapRef<typeof import('vue')['computed']>
    readonly createError: UnwrapRef<typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/error')['createError']>
    readonly customRef: UnwrapRef<typeof import('vue')['customRef']>
    readonly debounce: UnwrapRef<typeof import('../../utils/index')['debounce']>
    readonly defineAppConfig: UnwrapRef<typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/nuxt')['defineAppConfig']>
    readonly defineAsyncComponent: UnwrapRef<typeof import('vue')['defineAsyncComponent']>
    readonly defineComponent: UnwrapRef<typeof import('vue')['defineComponent']>
    readonly defineNuxtComponent: UnwrapRef<typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/component')['defineNuxtComponent']>
    readonly defineNuxtLink: UnwrapRef<typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/components/nuxt-link')['defineNuxtLink']>
    readonly defineNuxtPlugin: UnwrapRef<typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/nuxt')['defineNuxtPlugin']>
    readonly defineNuxtRouteMiddleware: UnwrapRef<typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/router')['defineNuxtRouteMiddleware']>
    readonly definePageMeta: UnwrapRef<typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/pages/runtime/composables')['definePageMeta']>
    readonly definePayloadPlugin: UnwrapRef<typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/nuxt')['definePayloadPlugin']>
    readonly definePayloadReducer: UnwrapRef<typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/payload')['definePayloadReducer']>
    readonly definePayloadReviver: UnwrapRef<typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/payload')['definePayloadReviver']>
    readonly defineStore: UnwrapRef<typeof import('../../../../node_modules/.pnpm/@pinia+nuxt@0.11.1_pinia@3.0.3/node_modules/@pinia/nuxt/dist/runtime/composables')['defineStore']>
    readonly delay: UnwrapRef<typeof import('../../utils/index')['delay']>
    readonly detectDeviceFromUserAgent: UnwrapRef<typeof import('../../composables/useDeviceDetection')['detectDeviceFromUserAgent']>
    readonly effect: UnwrapRef<typeof import('vue')['effect']>
    readonly effectScope: UnwrapRef<typeof import('vue')['effectScope']>
    readonly formatTimestamp: UnwrapRef<typeof import('../../utils/index')['formatTimestamp']>
    readonly generateUUID: UnwrapRef<typeof import('../../utils/index')['generateUUID']>
    readonly getAppManifest: UnwrapRef<typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/manifest')['getAppManifest']>
    readonly getCurrentInstance: UnwrapRef<typeof import('vue')['getCurrentInstance']>
    readonly getCurrentScope: UnwrapRef<typeof import('vue')['getCurrentScope']>
    readonly getRouteRules: UnwrapRef<typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/manifest')['getRouteRules']>
    readonly h: UnwrapRef<typeof import('vue')['h']>
    readonly hasInjectionContext: UnwrapRef<typeof import('vue')['hasInjectionContext']>
    readonly inject: UnwrapRef<typeof import('vue')['inject']>
    readonly injectHead: UnwrapRef<typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/head')['injectHead']>
    readonly isAndroid: UnwrapRef<typeof import('../../utils/index')['isAndroid']>
    readonly isIOS: UnwrapRef<typeof import('../../utils/index')['isIOS']>
    readonly isMobile: UnwrapRef<typeof import('../../utils/index')['isMobile']>
    readonly isNuxtError: UnwrapRef<typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/error')['isNuxtError']>
    readonly isPrerendered: UnwrapRef<typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/payload')['isPrerendered']>
    readonly isProxy: UnwrapRef<typeof import('vue')['isProxy']>
    readonly isReactive: UnwrapRef<typeof import('vue')['isReactive']>
    readonly isReadonly: UnwrapRef<typeof import('vue')['isReadonly']>
    readonly isRef: UnwrapRef<typeof import('vue')['isRef']>
    readonly isShallow: UnwrapRef<typeof import('vue')['isShallow']>
    readonly isVue2: UnwrapRef<typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/compat/vue-demi')['isVue2']>
    readonly isVue3: UnwrapRef<typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/compat/vue-demi')['isVue3']>
    readonly loadPayload: UnwrapRef<typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/payload')['loadPayload']>
    readonly markRaw: UnwrapRef<typeof import('vue')['markRaw']>
    readonly mergeModels: UnwrapRef<typeof import('vue')['mergeModels']>
    readonly message: UnwrapRef<typeof import('../../composables/useMessage')['message']>
    readonly navigateTo: UnwrapRef<typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/router')['navigateTo']>
    readonly nextTick: UnwrapRef<typeof import('vue')['nextTick']>
    readonly onActivated: UnwrapRef<typeof import('vue')['onActivated']>
    readonly onBeforeMount: UnwrapRef<typeof import('vue')['onBeforeMount']>
    readonly onBeforeRouteLeave: UnwrapRef<typeof import('vue-router')['onBeforeRouteLeave']>
    readonly onBeforeRouteUpdate: UnwrapRef<typeof import('vue-router')['onBeforeRouteUpdate']>
    readonly onBeforeUnmount: UnwrapRef<typeof import('vue')['onBeforeUnmount']>
    readonly onBeforeUpdate: UnwrapRef<typeof import('vue')['onBeforeUpdate']>
    readonly onDeactivated: UnwrapRef<typeof import('vue')['onDeactivated']>
    readonly onErrorCaptured: UnwrapRef<typeof import('vue')['onErrorCaptured']>
    readonly onMounted: UnwrapRef<typeof import('vue')['onMounted']>
    readonly onNuxtReady: UnwrapRef<typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/ready')['onNuxtReady']>
    readonly onPrehydrate: UnwrapRef<typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/ssr')['onPrehydrate']>
    readonly onRenderTracked: UnwrapRef<typeof import('vue')['onRenderTracked']>
    readonly onRenderTriggered: UnwrapRef<typeof import('vue')['onRenderTriggered']>
    readonly onScopeDispose: UnwrapRef<typeof import('vue')['onScopeDispose']>
    readonly onServerPrefetch: UnwrapRef<typeof import('vue')['onServerPrefetch']>
    readonly onUnmounted: UnwrapRef<typeof import('vue')['onUnmounted']>
    readonly onUpdated: UnwrapRef<typeof import('vue')['onUpdated']>
    readonly prefetchComponents: UnwrapRef<typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/preload')['prefetchComponents']>
    readonly preloadComponents: UnwrapRef<typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/preload')['preloadComponents']>
    readonly preloadPayload: UnwrapRef<typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/payload')['preloadPayload']>
    readonly preloadRouteComponents: UnwrapRef<typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/preload')['preloadRouteComponents']>
    readonly prerenderRoutes: UnwrapRef<typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/ssr')['prerenderRoutes']>
    readonly provide: UnwrapRef<typeof import('vue')['provide']>
    readonly proxyRefs: UnwrapRef<typeof import('vue')['proxyRefs']>
    readonly reactive: UnwrapRef<typeof import('vue')['reactive']>
    readonly readonly: UnwrapRef<typeof import('vue')['readonly']>
    readonly ref: UnwrapRef<typeof import('vue')['ref']>
    readonly refreshCookie: UnwrapRef<typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/cookie')['refreshCookie']>
    readonly refreshNuxtData: UnwrapRef<typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/asyncData')['refreshNuxtData']>
    readonly reloadNuxtApp: UnwrapRef<typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/chunk')['reloadNuxtApp']>
    readonly requestIdleCallback: UnwrapRef<typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/compat/idle-callback')['requestIdleCallback']>
    readonly resolveComponent: UnwrapRef<typeof import('vue')['resolveComponent']>
    readonly setInterval: UnwrapRef<typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/compat/interval')['setInterval']>
    readonly setMessageInstance: UnwrapRef<typeof import('../../composables/useMessage')['setMessageInstance']>
    readonly setPageLayout: UnwrapRef<typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/router')['setPageLayout']>
    readonly setResponseStatus: UnwrapRef<typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/ssr')['setResponseStatus']>
    readonly shallowReactive: UnwrapRef<typeof import('vue')['shallowReactive']>
    readonly shallowReadonly: UnwrapRef<typeof import('vue')['shallowReadonly']>
    readonly shallowRef: UnwrapRef<typeof import('vue')['shallowRef']>
    readonly showError: UnwrapRef<typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/error')['showError']>
    readonly storeToRefs: UnwrapRef<typeof import('../../../../node_modules/.pnpm/@pinia+nuxt@0.11.1_pinia@3.0.3/node_modules/@pinia/nuxt/dist/runtime/composables')['storeToRefs']>
    readonly throttle: UnwrapRef<typeof import('../../utils/index')['throttle']>
    readonly toRaw: UnwrapRef<typeof import('vue')['toRaw']>
    readonly toRef: UnwrapRef<typeof import('vue')['toRef']>
    readonly toRefs: UnwrapRef<typeof import('vue')['toRefs']>
    readonly toValue: UnwrapRef<typeof import('vue')['toValue']>
    readonly triggerRef: UnwrapRef<typeof import('vue')['triggerRef']>
    readonly tryUseNuxtApp: UnwrapRef<typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/nuxt')['tryUseNuxtApp']>
    readonly unref: UnwrapRef<typeof import('vue')['unref']>
    readonly updateAppConfig: UnwrapRef<typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/config')['updateAppConfig']>
    readonly useAnalytics: UnwrapRef<typeof import('../../composables/useAnalytics')['useAnalytics']>
    readonly useApi: UnwrapRef<typeof import('../../composables/useApi')['useApi']>
    readonly useAppConfig: UnwrapRef<typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/config')['useAppConfig']>
    readonly useAsyncData: UnwrapRef<typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/asyncData')['useAsyncData']>
    readonly useAttrs: UnwrapRef<typeof import('vue')['useAttrs']>
    readonly useBranding: UnwrapRef<typeof import('../../composables/useBranding')['useBranding']>
    readonly useCdn: UnwrapRef<typeof import('../../composables/useCdn')['useCdn']>
    readonly useCheckinStore: UnwrapRef<typeof import('../../stores/checkin')['useCheckinStore']>
    readonly useCookie: UnwrapRef<typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/cookie')['useCookie']>
    readonly useCssModule: UnwrapRef<typeof import('vue')['useCssModule']>
    readonly useCssVars: UnwrapRef<typeof import('vue')['useCssVars']>
    readonly useDataManager: UnwrapRef<typeof import('../../composables/useDataManager')['useDataManager']>
    readonly useDeviceDetection: UnwrapRef<typeof import('../../composables/useDeviceDetection')['useDeviceDetection']>
    readonly useError: UnwrapRef<typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/error')['useError']>
    readonly useFetch: UnwrapRef<typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/fetch')['useFetch']>
    readonly useFilterSeo: UnwrapRef<typeof import('../../composables/useFilterSeo')['useFilterSeo']>
    readonly useGameStructuredData: UnwrapRef<typeof import('../../composables/useGameStructuredData')['useGameStructuredData']>
    readonly useGtag: UnwrapRef<typeof import('../../../../node_modules/.pnpm/nuxt-gtag@3.0.3/node_modules/nuxt-gtag/dist/runtime/composables/useGtag')['useGtag']>
    readonly useHead: UnwrapRef<typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/head')['useHead']>
    readonly useHeadSafe: UnwrapRef<typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/head')['useHeadSafe']>
    readonly useHydration: UnwrapRef<typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/hydrate')['useHydration']>
    readonly useId: UnwrapRef<typeof import('vue')['useId']>
    readonly useImage: UnwrapRef<typeof import('../../../../node_modules/.pnpm/@nuxt+image@1.10.0/node_modules/@nuxt/image/dist/runtime/composables')['useImage']>
    readonly useLazyAsyncData: UnwrapRef<typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/asyncData')['useLazyAsyncData']>
    readonly useLazyFetch: UnwrapRef<typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/fetch')['useLazyFetch']>
    readonly useLink: UnwrapRef<typeof import('vue-router')['useLink']>
    readonly useLoadingIndicator: UnwrapRef<typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/loading-indicator')['useLoadingIndicator']>
    readonly useLoadingStore: UnwrapRef<typeof import('../../stores/loading')['useLoadingStore']>
    readonly useMenuHeight: UnwrapRef<typeof import('../../composables/useMenuHeight')['useMenuHeight']>
    readonly useMessage: UnwrapRef<typeof import('../../composables/useMessage')['useMessage']>
    readonly useModel: UnwrapRef<typeof import('vue')['useModel']>
    readonly useNuxtApp: UnwrapRef<typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/nuxt')['useNuxtApp']>
    readonly useNuxtData: UnwrapRef<typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/asyncData')['useNuxtData']>
    readonly useNuxtDevTools: UnwrapRef<typeof import('../../../../node_modules/.pnpm/@nuxt+devtools@2.6.2_vite@6.3.5_vue@3.5.17/node_modules/@nuxt/devtools/dist/runtime/use-nuxt-devtools')['useNuxtDevTools']>
    readonly usePageSeo: UnwrapRef<typeof import('../../composables/usePageSeo')['usePageSeo']>
    readonly usePaymentConfig: UnwrapRef<typeof import('../../composables/usePaymentConfig')['usePaymentConfig']>
    readonly usePinia: UnwrapRef<typeof import('../../../../node_modules/.pnpm/@pinia+nuxt@0.11.1_pinia@3.0.3/node_modules/@pinia/nuxt/dist/runtime/composables')['usePinia']>
    readonly usePreviewMode: UnwrapRef<typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/preview')['usePreviewMode']>
    readonly useRechargeStore: UnwrapRef<typeof import('../../stores/recharge')['useRechargeStore']>
    readonly useRequestEvent: UnwrapRef<typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/ssr')['useRequestEvent']>
    readonly useRequestFetch: UnwrapRef<typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/ssr')['useRequestFetch']>
    readonly useRequestHeader: UnwrapRef<typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/ssr')['useRequestHeader']>
    readonly useRequestHeaders: UnwrapRef<typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/ssr')['useRequestHeaders']>
    readonly useRequestURL: UnwrapRef<typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/url')['useRequestURL']>
    readonly useResponseHeader: UnwrapRef<typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/ssr')['useResponseHeader']>
    readonly useRoute: UnwrapRef<typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/router')['useRoute']>
    readonly useRouteAnnouncer: UnwrapRef<typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/route-announcer')['useRouteAnnouncer']>
    readonly useRouter: UnwrapRef<typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/router')['useRouter']>
    readonly useRuntimeConfig: UnwrapRef<typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/nuxt')['useRuntimeConfig']>
    readonly useRuntimeHook: UnwrapRef<typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/runtime-hook')['useRuntimeHook']>
    readonly useScript: UnwrapRef<typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/script-stubs')['useScript']>
    readonly useScriptClarity: UnwrapRef<typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/script-stubs')['useScriptClarity']>
    readonly useScriptCloudflareWebAnalytics: UnwrapRef<typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/script-stubs')['useScriptCloudflareWebAnalytics']>
    readonly useScriptCrisp: UnwrapRef<typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/script-stubs')['useScriptCrisp']>
    readonly useScriptEventPage: UnwrapRef<typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/script-stubs')['useScriptEventPage']>
    readonly useScriptFathomAnalytics: UnwrapRef<typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/script-stubs')['useScriptFathomAnalytics']>
    readonly useScriptGoogleAdsense: UnwrapRef<typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/script-stubs')['useScriptGoogleAdsense']>
    readonly useScriptGoogleAnalytics: UnwrapRef<typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/script-stubs')['useScriptGoogleAnalytics']>
    readonly useScriptGoogleMaps: UnwrapRef<typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/script-stubs')['useScriptGoogleMaps']>
    readonly useScriptGoogleTagManager: UnwrapRef<typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/script-stubs')['useScriptGoogleTagManager']>
    readonly useScriptHotjar: UnwrapRef<typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/script-stubs')['useScriptHotjar']>
    readonly useScriptIntercom: UnwrapRef<typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/script-stubs')['useScriptIntercom']>
    readonly useScriptLemonSqueezy: UnwrapRef<typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/script-stubs')['useScriptLemonSqueezy']>
    readonly useScriptMatomoAnalytics: UnwrapRef<typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/script-stubs')['useScriptMatomoAnalytics']>
    readonly useScriptMetaPixel: UnwrapRef<typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/script-stubs')['useScriptMetaPixel']>
    readonly useScriptNpm: UnwrapRef<typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/script-stubs')['useScriptNpm']>
    readonly useScriptPlausibleAnalytics: UnwrapRef<typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/script-stubs')['useScriptPlausibleAnalytics']>
    readonly useScriptRybbitAnalytics: UnwrapRef<typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/script-stubs')['useScriptRybbitAnalytics']>
    readonly useScriptSegment: UnwrapRef<typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/script-stubs')['useScriptSegment']>
    readonly useScriptSnapchatPixel: UnwrapRef<typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/script-stubs')['useScriptSnapchatPixel']>
    readonly useScriptStripe: UnwrapRef<typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/script-stubs')['useScriptStripe']>
    readonly useScriptTriggerConsent: UnwrapRef<typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/script-stubs')['useScriptTriggerConsent']>
    readonly useScriptTriggerElement: UnwrapRef<typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/script-stubs')['useScriptTriggerElement']>
    readonly useScriptUmamiAnalytics: UnwrapRef<typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/script-stubs')['useScriptUmamiAnalytics']>
    readonly useScriptVimeoPlayer: UnwrapRef<typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/script-stubs')['useScriptVimeoPlayer']>
    readonly useScriptXPixel: UnwrapRef<typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/script-stubs')['useScriptXPixel']>
    readonly useScriptYouTubePlayer: UnwrapRef<typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/script-stubs')['useScriptYouTubePlayer']>
    readonly useSeoMeta: UnwrapRef<typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/head')['useSeoMeta']>
    readonly useServerHead: UnwrapRef<typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/head')['useServerHead']>
    readonly useServerHeadSafe: UnwrapRef<typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/head')['useServerHeadSafe']>
    readonly useServerSeoMeta: UnwrapRef<typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/head')['useServerSeoMeta']>
    readonly useShadowRoot: UnwrapRef<typeof import('vue')['useShadowRoot']>
    readonly useSlots: UnwrapRef<typeof import('vue')['useSlots']>
    readonly useState: UnwrapRef<typeof import('../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/composables/state')['useState']>
    readonly useStories: UnwrapRef<typeof import('../../composables/useStories')['useStories']>
    readonly useStoriesEnhancer: UnwrapRef<typeof import('../../composables/useStoriesEnhancer')['useStoriesEnhancer']>
    readonly useStoriesV2: UnwrapRef<typeof import('../../composables/useStoriesV2')['useStoriesV2']>
    readonly useStoryStore: UnwrapRef<typeof import('../../stores/story')['useStoryStore']>
    readonly useSysConfigStore: UnwrapRef<typeof import('../../stores/sysconfig')['useSysConfigStore']>
    readonly useTagsFilter: UnwrapRef<typeof import('../../composables/useTagsFilter')['useTagsFilter']>
    readonly useTasksStore: UnwrapRef<typeof import('../../stores/tasks')['useTasksStore']>
    readonly useTemplateRef: UnwrapRef<typeof import('vue')['useTemplateRef']>
    readonly useThemeStore: UnwrapRef<typeof import('../../stores/theme')['useThemeStore']>
    readonly useTokenManager: UnwrapRef<typeof import('../../composables/useTokenManager')['useTokenManager']>
    readonly useTrackEvent: UnwrapRef<typeof import('../../../../node_modules/.pnpm/nuxt-gtag@3.0.3/node_modules/nuxt-gtag/dist/runtime/composables/useTrackEvent')['useTrackEvent']>
    readonly useTransitionState: UnwrapRef<typeof import('vue')['useTransitionState']>
    readonly useUserStore: UnwrapRef<typeof import('../../stores/user')['useUserStore']>
    readonly watch: UnwrapRef<typeof import('vue')['watch']>
    readonly watchEffect: UnwrapRef<typeof import('vue')['watchEffect']>
    readonly watchPostEffect: UnwrapRef<typeof import('vue')['watchPostEffect']>
    readonly watchSyncEffect: UnwrapRef<typeof import('vue')['watchSyncEffect']>
    readonly withCtx: UnwrapRef<typeof import('vue')['withCtx']>
    readonly withDirectives: UnwrapRef<typeof import('vue')['withDirectives']>
    readonly withKeys: UnwrapRef<typeof import('vue')['withKeys']>
    readonly withMemo: UnwrapRef<typeof import('vue')['withMemo']>
    readonly withModifiers: UnwrapRef<typeof import('vue')['withModifiers']>
    readonly withScopeId: UnwrapRef<typeof import('vue')['withScopeId']>
  }
}