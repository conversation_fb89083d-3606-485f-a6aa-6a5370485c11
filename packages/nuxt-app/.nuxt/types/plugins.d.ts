// Generated by Nuxt'
import type { Plugin } from '#app'

type Decorate<T extends Record<string, any>> = { [K in keyof T as K extends string ? `$${K}` : never]: T[K] }

type InjectionType<A extends Plugin> = A extends {default: Plugin<infer T>} ? Decorate<T> : unknown

type NuxtAppInjections = 
  InjectionType<typeof import("../../../../node_modules/.pnpm/@pinia+nuxt@0.11.1_pinia@3.0.3/node_modules/@pinia/nuxt/dist/runtime/payload-plugin.js")> &
  InjectionType<typeof import("../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/plugins/revive-payload.client.js")> &
  InjectionType<typeof import("../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/head/runtime/plugins/unhead.js")> &
  InjectionType<typeof import("../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/pages/runtime/plugins/router.js")> &
  InjectionType<typeof import("../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/plugins/browser-devtools-timing.client.js")> &
  InjectionType<typeof import("../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/plugins/navigation-repaint.client.js")> &
  InjectionType<typeof import("../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/plugins/check-outdated-build.client.js")> &
  InjectionType<typeof import("../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/plugins/revive-payload.server.js")> &
  InjectionType<typeof import("../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/plugins/chunk-reload.client.js")> &
  InjectionType<typeof import("../../../../node_modules/.pnpm/@pinia+nuxt@0.11.1_pinia@3.0.3/node_modules/@pinia/nuxt/dist/runtime/plugin.vue3.js")> &
  InjectionType<typeof import("../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/pages/runtime/plugins/prefetch.client.js")> &
  InjectionType<typeof import("../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/pages/runtime/plugins/check-if-page-unused.js")> &
  InjectionType<typeof import("../../../../node_modules/.pnpm/@nuxt+devtools@2.6.2_vite@6.3.5_vue@3.5.17/node_modules/@nuxt/devtools/dist/runtime/plugins/devtools.server.js")> &
  InjectionType<typeof import("../../../../node_modules/.pnpm/@nuxt+devtools@2.6.2_vite@6.3.5_vue@3.5.17/node_modules/@nuxt/devtools/dist/runtime/plugins/devtools.client.js")> &
  InjectionType<typeof import("../../../../node_modules/.pnpm/nuxt-gtag@3.0.3/node_modules/nuxt-gtag/dist/runtime/plugin.client.js")> &
  InjectionType<typeof import("../../../../node_modules/.pnpm/@nuxt+icon@1.14.0_vite@6.3.5_vue@3.5.17/node_modules/@nuxt/icon/dist/runtime/plugin.js")> &
  InjectionType<typeof import("../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/plugins/dev-server-logs.js")> &
  InjectionType<typeof import("../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/plugins/check-if-layout-used.js")> &
  InjectionType<typeof import("../../plugins/pinia.client")> &
  InjectionType<typeof import("../../plugins/smart-auth.client")> &
  InjectionType<typeof import("../../plugins/viewport-height.client")> &
  InjectionType<typeof import("../../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/pages/runtime/plugins/prerender.server.js")>

declare module '#app' {
  interface NuxtApp extends NuxtAppInjections { }

  interface NuxtAppLiterals {
    pluginName: 'vue-devtools-client' | 'nuxt:revive-payload:client' | 'nuxt:head' | 'nuxt:router' | 'nuxt:browser-devtools-timing' | 'nuxt:revive-payload:server' | 'nuxt:chunk-reload' | 'pinia' | 'nuxt:global-components' | 'nuxt:prefetch' | 'nuxt:checkIfPageUnused' | '@nuxt/icon' | 'nuxt:checkIfLayoutUsed'
  }
}

declare module 'vue' {
  interface ComponentCustomProperties extends NuxtAppInjections { }
}

export { }
