
import type { DefineComponent, SlotsType } from 'vue'
type IslandComponent<T extends DefineComponent> = T & DefineComponent<{}, {refresh: () => Promise<void>}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, SlotsType<{ fallback: { error: unknown } }>>
type HydrationStrategies = {
  hydrateOnVisible?: IntersectionObserverInit | true
  hydrateOnIdle?: number | true
  hydrateOnInteraction?: keyof HTMLElementEventMap | Array<keyof HTMLElementEventMap> | true
  hydrateOnMediaQuery?: string
  hydrateAfter?: number
  hydrateWhen?: boolean
  hydrateNever?: true
}
type LazyComponent<T> = (T & DefineComponent<HydrationStrategies, {}, {}, {}, {}, {}, {}, { hydrated: () => void }>)
interface _GlobalComponents {
      'AdaptiveLayout': typeof import("../components/AdaptiveLayout.vue")['default']
    'AppFooter': typeof import("../components/AppFooter.vue")['default']
    'AppHeader': typeof import("../components/AppHeader.vue")['default']
    'AppMenu': typeof import("../components/AppMenu.vue")['default']
    'AppMessage': typeof import("../components/AppMessage.vue")['default']
    'AuthModal': typeof import("../components/AuthModal.vue")['default']
    'CategoryDrawer': typeof import("../components/CategoryDrawer.vue")['default']
    'CharacterSelectDrawer': typeof import("../components/CharacterSelectDrawer.vue")['default']
    'CharacterSelectModal': typeof import("../components/CharacterSelectModal.vue")['default']
    'ChatHistoryConfirmDialog': typeof import("../components/ChatHistoryConfirmDialog.vue")['default']
    'CheckinModal': typeof import("../components/CheckinModal.vue")['default']
    'ConfirmDialog': typeof import("../components/ConfirmDialog.vue")['default']
    'CreditDisplay': typeof import("../components/CreditDisplay.vue")['default']
    'FilterDropdown': typeof import("../components/FilterDropdown.vue")['default']
    'GlobalLoading': typeof import("../components/GlobalLoading.vue")['default']
    'HomePageMobile': typeof import("../components/HomePageMobile.vue")['default']
    'HomePagePC': typeof import("../components/HomePagePC.vue")['default']
    'InviteCodeModal': typeof import("../components/InviteCodeModal.vue")['default']
    'LegalPageLayout': typeof import("../components/LegalPageLayout.vue")['default']
    'MobileLayout': typeof import("../components/MobileLayout.vue")['default']
    'MobileProfile': typeof import("../components/MobileProfile.vue")['default']
    'MobileSettings': typeof import("../components/MobileSettings.vue")['default']
    'PCHeader': typeof import("../components/PCHeader.vue")['default']
    'PCLayout': typeof import("../components/PCLayout.vue")['default']
    'PCModal': typeof import("../components/PCModal.vue")['default']
    'PCProfile': typeof import("../components/PCProfile.vue")['default']
    'PCSidebar': typeof import("../components/PCSidebar.vue")['default']
    'RechargeModal': typeof import("../components/RechargeModal.vue")['default']
    'RemoteChatLoader': typeof import("../components/RemoteChatLoader.vue")['default']
    'StoryCard': typeof import("../components/StoryCard.vue")['default']
    'StoryDetailModal': typeof import("../components/StoryDetailModal.vue")['default']
    'VirtualStoryGrid': typeof import("../components/VirtualStoryGrid.vue")['default']
    'NuxtWelcome': typeof import("../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/components/welcome.vue")['default']
    'NuxtLayout': typeof import("../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/components/nuxt-layout")['default']
    'NuxtErrorBoundary': typeof import("../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/components/nuxt-error-boundary.vue")['default']
    'ClientOnly': typeof import("../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/components/client-only")['default']
    'DevOnly': typeof import("../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/components/dev-only")['default']
    'ServerPlaceholder': typeof import("../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/components/server-placeholder")['default']
    'NuxtLink': typeof import("../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/components/nuxt-link")['default']
    'NuxtLoadingIndicator': typeof import("../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/components/nuxt-loading-indicator")['default']
    'NuxtTime': typeof import("../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/components/nuxt-time.vue")['default']
    'NuxtRouteAnnouncer': typeof import("../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/components/nuxt-route-announcer")['default']
    'NuxtImg': typeof import("../../../node_modules/.pnpm/@nuxt+image@1.10.0/node_modules/@nuxt/image/dist/runtime/components/NuxtImg.vue")['default']
    'NuxtPicture': typeof import("../../../node_modules/.pnpm/@nuxt+image@1.10.0/node_modules/@nuxt/image/dist/runtime/components/NuxtPicture.vue")['default']
    'Icon': typeof import("../../../node_modules/.pnpm/@nuxt+icon@1.14.0_vite@6.3.5_vue@3.5.17/node_modules/@nuxt/icon/dist/runtime/components/index")['default']
    'NuxtPage': typeof import("../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/pages/runtime/page")['default']
    'NoScript': typeof import("../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/head/runtime/components")['NoScript']
    'Link': typeof import("../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/head/runtime/components")['Link']
    'Base': typeof import("../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/head/runtime/components")['Base']
    'Title': typeof import("../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/head/runtime/components")['Title']
    'Meta': typeof import("../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/head/runtime/components")['Meta']
    'Style': typeof import("../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/head/runtime/components")['Style']
    'Head': typeof import("../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/head/runtime/components")['Head']
    'Html': typeof import("../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/head/runtime/components")['Html']
    'Body': typeof import("../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/head/runtime/components")['Body']
    'NuxtIsland': typeof import("../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/components/nuxt-island")['default']
    'NuxtRouteAnnouncer': IslandComponent<typeof import("../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/components/server-placeholder")['default']>
      'LazyAdaptiveLayout': LazyComponent<typeof import("../components/AdaptiveLayout.vue")['default']>
    'LazyAppFooter': LazyComponent<typeof import("../components/AppFooter.vue")['default']>
    'LazyAppHeader': LazyComponent<typeof import("../components/AppHeader.vue")['default']>
    'LazyAppMenu': LazyComponent<typeof import("../components/AppMenu.vue")['default']>
    'LazyAppMessage': LazyComponent<typeof import("../components/AppMessage.vue")['default']>
    'LazyAuthModal': LazyComponent<typeof import("../components/AuthModal.vue")['default']>
    'LazyCategoryDrawer': LazyComponent<typeof import("../components/CategoryDrawer.vue")['default']>
    'LazyCharacterSelectDrawer': LazyComponent<typeof import("../components/CharacterSelectDrawer.vue")['default']>
    'LazyCharacterSelectModal': LazyComponent<typeof import("../components/CharacterSelectModal.vue")['default']>
    'LazyChatHistoryConfirmDialog': LazyComponent<typeof import("../components/ChatHistoryConfirmDialog.vue")['default']>
    'LazyCheckinModal': LazyComponent<typeof import("../components/CheckinModal.vue")['default']>
    'LazyConfirmDialog': LazyComponent<typeof import("../components/ConfirmDialog.vue")['default']>
    'LazyCreditDisplay': LazyComponent<typeof import("../components/CreditDisplay.vue")['default']>
    'LazyFilterDropdown': LazyComponent<typeof import("../components/FilterDropdown.vue")['default']>
    'LazyGlobalLoading': LazyComponent<typeof import("../components/GlobalLoading.vue")['default']>
    'LazyHomePageMobile': LazyComponent<typeof import("../components/HomePageMobile.vue")['default']>
    'LazyHomePagePC': LazyComponent<typeof import("../components/HomePagePC.vue")['default']>
    'LazyInviteCodeModal': LazyComponent<typeof import("../components/InviteCodeModal.vue")['default']>
    'LazyLegalPageLayout': LazyComponent<typeof import("../components/LegalPageLayout.vue")['default']>
    'LazyMobileLayout': LazyComponent<typeof import("../components/MobileLayout.vue")['default']>
    'LazyMobileProfile': LazyComponent<typeof import("../components/MobileProfile.vue")['default']>
    'LazyMobileSettings': LazyComponent<typeof import("../components/MobileSettings.vue")['default']>
    'LazyPCHeader': LazyComponent<typeof import("../components/PCHeader.vue")['default']>
    'LazyPCLayout': LazyComponent<typeof import("../components/PCLayout.vue")['default']>
    'LazyPCModal': LazyComponent<typeof import("../components/PCModal.vue")['default']>
    'LazyPCProfile': LazyComponent<typeof import("../components/PCProfile.vue")['default']>
    'LazyPCSidebar': LazyComponent<typeof import("../components/PCSidebar.vue")['default']>
    'LazyRechargeModal': LazyComponent<typeof import("../components/RechargeModal.vue")['default']>
    'LazyRemoteChatLoader': LazyComponent<typeof import("../components/RemoteChatLoader.vue")['default']>
    'LazyStoryCard': LazyComponent<typeof import("../components/StoryCard.vue")['default']>
    'LazyStoryDetailModal': LazyComponent<typeof import("../components/StoryDetailModal.vue")['default']>
    'LazyVirtualStoryGrid': LazyComponent<typeof import("../components/VirtualStoryGrid.vue")['default']>
    'LazyNuxtWelcome': LazyComponent<typeof import("../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/components/welcome.vue")['default']>
    'LazyNuxtLayout': LazyComponent<typeof import("../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/components/nuxt-layout")['default']>
    'LazyNuxtErrorBoundary': LazyComponent<typeof import("../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/components/nuxt-error-boundary.vue")['default']>
    'LazyClientOnly': LazyComponent<typeof import("../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/components/client-only")['default']>
    'LazyDevOnly': LazyComponent<typeof import("../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/components/dev-only")['default']>
    'LazyServerPlaceholder': LazyComponent<typeof import("../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/components/server-placeholder")['default']>
    'LazyNuxtLink': LazyComponent<typeof import("../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/components/nuxt-link")['default']>
    'LazyNuxtLoadingIndicator': LazyComponent<typeof import("../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/components/nuxt-loading-indicator")['default']>
    'LazyNuxtTime': LazyComponent<typeof import("../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/components/nuxt-time.vue")['default']>
    'LazyNuxtRouteAnnouncer': LazyComponent<typeof import("../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/components/nuxt-route-announcer")['default']>
    'LazyNuxtImg': LazyComponent<typeof import("../../../node_modules/.pnpm/@nuxt+image@1.10.0/node_modules/@nuxt/image/dist/runtime/components/NuxtImg.vue")['default']>
    'LazyNuxtPicture': LazyComponent<typeof import("../../../node_modules/.pnpm/@nuxt+image@1.10.0/node_modules/@nuxt/image/dist/runtime/components/NuxtPicture.vue")['default']>
    'LazyIcon': LazyComponent<typeof import("../../../node_modules/.pnpm/@nuxt+icon@1.14.0_vite@6.3.5_vue@3.5.17/node_modules/@nuxt/icon/dist/runtime/components/index")['default']>
    'LazyNuxtPage': LazyComponent<typeof import("../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/pages/runtime/page")['default']>
    'LazyNoScript': LazyComponent<typeof import("../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/head/runtime/components")['NoScript']>
    'LazyLink': LazyComponent<typeof import("../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/head/runtime/components")['Link']>
    'LazyBase': LazyComponent<typeof import("../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/head/runtime/components")['Base']>
    'LazyTitle': LazyComponent<typeof import("../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/head/runtime/components")['Title']>
    'LazyMeta': LazyComponent<typeof import("../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/head/runtime/components")['Meta']>
    'LazyStyle': LazyComponent<typeof import("../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/head/runtime/components")['Style']>
    'LazyHead': LazyComponent<typeof import("../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/head/runtime/components")['Head']>
    'LazyHtml': LazyComponent<typeof import("../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/head/runtime/components")['Html']>
    'LazyBody': LazyComponent<typeof import("../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/head/runtime/components")['Body']>
    'LazyNuxtIsland': LazyComponent<typeof import("../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/components/nuxt-island")['default']>
    'LazyNuxtRouteAnnouncer': LazyComponent<IslandComponent<typeof import("../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/components/server-placeholder")['default']>>
}

declare module 'vue' {
  export interface GlobalComponents extends _GlobalComponents { }
}

export const AdaptiveLayout: typeof import("../components/AdaptiveLayout.vue")['default']
export const AppFooter: typeof import("../components/AppFooter.vue")['default']
export const AppHeader: typeof import("../components/AppHeader.vue")['default']
export const AppMenu: typeof import("../components/AppMenu.vue")['default']
export const AppMessage: typeof import("../components/AppMessage.vue")['default']
export const AuthModal: typeof import("../components/AuthModal.vue")['default']
export const CategoryDrawer: typeof import("../components/CategoryDrawer.vue")['default']
export const CharacterSelectDrawer: typeof import("../components/CharacterSelectDrawer.vue")['default']
export const CharacterSelectModal: typeof import("../components/CharacterSelectModal.vue")['default']
export const ChatHistoryConfirmDialog: typeof import("../components/ChatHistoryConfirmDialog.vue")['default']
export const CheckinModal: typeof import("../components/CheckinModal.vue")['default']
export const ConfirmDialog: typeof import("../components/ConfirmDialog.vue")['default']
export const CreditDisplay: typeof import("../components/CreditDisplay.vue")['default']
export const FilterDropdown: typeof import("../components/FilterDropdown.vue")['default']
export const GlobalLoading: typeof import("../components/GlobalLoading.vue")['default']
export const HomePageMobile: typeof import("../components/HomePageMobile.vue")['default']
export const HomePagePC: typeof import("../components/HomePagePC.vue")['default']
export const InviteCodeModal: typeof import("../components/InviteCodeModal.vue")['default']
export const LegalPageLayout: typeof import("../components/LegalPageLayout.vue")['default']
export const MobileLayout: typeof import("../components/MobileLayout.vue")['default']
export const MobileProfile: typeof import("../components/MobileProfile.vue")['default']
export const MobileSettings: typeof import("../components/MobileSettings.vue")['default']
export const PCHeader: typeof import("../components/PCHeader.vue")['default']
export const PCLayout: typeof import("../components/PCLayout.vue")['default']
export const PCModal: typeof import("../components/PCModal.vue")['default']
export const PCProfile: typeof import("../components/PCProfile.vue")['default']
export const PCSidebar: typeof import("../components/PCSidebar.vue")['default']
export const RechargeModal: typeof import("../components/RechargeModal.vue")['default']
export const RemoteChatLoader: typeof import("../components/RemoteChatLoader.vue")['default']
export const StoryCard: typeof import("../components/StoryCard.vue")['default']
export const StoryDetailModal: typeof import("../components/StoryDetailModal.vue")['default']
export const VirtualStoryGrid: typeof import("../components/VirtualStoryGrid.vue")['default']
export const NuxtWelcome: typeof import("../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/components/welcome.vue")['default']
export const NuxtLayout: typeof import("../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/components/nuxt-layout")['default']
export const NuxtErrorBoundary: typeof import("../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/components/nuxt-error-boundary.vue")['default']
export const ClientOnly: typeof import("../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/components/client-only")['default']
export const DevOnly: typeof import("../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/components/dev-only")['default']
export const ServerPlaceholder: typeof import("../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/components/server-placeholder")['default']
export const NuxtLink: typeof import("../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/components/nuxt-link")['default']
export const NuxtLoadingIndicator: typeof import("../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/components/nuxt-loading-indicator")['default']
export const NuxtTime: typeof import("../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/components/nuxt-time.vue")['default']
export const NuxtRouteAnnouncer: typeof import("../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/components/nuxt-route-announcer")['default']
export const NuxtImg: typeof import("../../../node_modules/.pnpm/@nuxt+image@1.10.0/node_modules/@nuxt/image/dist/runtime/components/NuxtImg.vue")['default']
export const NuxtPicture: typeof import("../../../node_modules/.pnpm/@nuxt+image@1.10.0/node_modules/@nuxt/image/dist/runtime/components/NuxtPicture.vue")['default']
export const Icon: typeof import("../../../node_modules/.pnpm/@nuxt+icon@1.14.0_vite@6.3.5_vue@3.5.17/node_modules/@nuxt/icon/dist/runtime/components/index")['default']
export const NuxtPage: typeof import("../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/pages/runtime/page")['default']
export const NoScript: typeof import("../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/head/runtime/components")['NoScript']
export const Link: typeof import("../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/head/runtime/components")['Link']
export const Base: typeof import("../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/head/runtime/components")['Base']
export const Title: typeof import("../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/head/runtime/components")['Title']
export const Meta: typeof import("../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/head/runtime/components")['Meta']
export const Style: typeof import("../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/head/runtime/components")['Style']
export const Head: typeof import("../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/head/runtime/components")['Head']
export const Html: typeof import("../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/head/runtime/components")['Html']
export const Body: typeof import("../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/head/runtime/components")['Body']
export const NuxtIsland: typeof import("../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/components/nuxt-island")['default']
export const NuxtRouteAnnouncer: IslandComponent<typeof import("../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/components/server-placeholder")['default']>
export const LazyAdaptiveLayout: LazyComponent<typeof import("../components/AdaptiveLayout.vue")['default']>
export const LazyAppFooter: LazyComponent<typeof import("../components/AppFooter.vue")['default']>
export const LazyAppHeader: LazyComponent<typeof import("../components/AppHeader.vue")['default']>
export const LazyAppMenu: LazyComponent<typeof import("../components/AppMenu.vue")['default']>
export const LazyAppMessage: LazyComponent<typeof import("../components/AppMessage.vue")['default']>
export const LazyAuthModal: LazyComponent<typeof import("../components/AuthModal.vue")['default']>
export const LazyCategoryDrawer: LazyComponent<typeof import("../components/CategoryDrawer.vue")['default']>
export const LazyCharacterSelectDrawer: LazyComponent<typeof import("../components/CharacterSelectDrawer.vue")['default']>
export const LazyCharacterSelectModal: LazyComponent<typeof import("../components/CharacterSelectModal.vue")['default']>
export const LazyChatHistoryConfirmDialog: LazyComponent<typeof import("../components/ChatHistoryConfirmDialog.vue")['default']>
export const LazyCheckinModal: LazyComponent<typeof import("../components/CheckinModal.vue")['default']>
export const LazyConfirmDialog: LazyComponent<typeof import("../components/ConfirmDialog.vue")['default']>
export const LazyCreditDisplay: LazyComponent<typeof import("../components/CreditDisplay.vue")['default']>
export const LazyFilterDropdown: LazyComponent<typeof import("../components/FilterDropdown.vue")['default']>
export const LazyGlobalLoading: LazyComponent<typeof import("../components/GlobalLoading.vue")['default']>
export const LazyHomePageMobile: LazyComponent<typeof import("../components/HomePageMobile.vue")['default']>
export const LazyHomePagePC: LazyComponent<typeof import("../components/HomePagePC.vue")['default']>
export const LazyInviteCodeModal: LazyComponent<typeof import("../components/InviteCodeModal.vue")['default']>
export const LazyLegalPageLayout: LazyComponent<typeof import("../components/LegalPageLayout.vue")['default']>
export const LazyMobileLayout: LazyComponent<typeof import("../components/MobileLayout.vue")['default']>
export const LazyMobileProfile: LazyComponent<typeof import("../components/MobileProfile.vue")['default']>
export const LazyMobileSettings: LazyComponent<typeof import("../components/MobileSettings.vue")['default']>
export const LazyPCHeader: LazyComponent<typeof import("../components/PCHeader.vue")['default']>
export const LazyPCLayout: LazyComponent<typeof import("../components/PCLayout.vue")['default']>
export const LazyPCModal: LazyComponent<typeof import("../components/PCModal.vue")['default']>
export const LazyPCProfile: LazyComponent<typeof import("../components/PCProfile.vue")['default']>
export const LazyPCSidebar: LazyComponent<typeof import("../components/PCSidebar.vue")['default']>
export const LazyRechargeModal: LazyComponent<typeof import("../components/RechargeModal.vue")['default']>
export const LazyRemoteChatLoader: LazyComponent<typeof import("../components/RemoteChatLoader.vue")['default']>
export const LazyStoryCard: LazyComponent<typeof import("../components/StoryCard.vue")['default']>
export const LazyStoryDetailModal: LazyComponent<typeof import("../components/StoryDetailModal.vue")['default']>
export const LazyVirtualStoryGrid: LazyComponent<typeof import("../components/VirtualStoryGrid.vue")['default']>
export const LazyNuxtWelcome: LazyComponent<typeof import("../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/components/welcome.vue")['default']>
export const LazyNuxtLayout: LazyComponent<typeof import("../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/components/nuxt-layout")['default']>
export const LazyNuxtErrorBoundary: LazyComponent<typeof import("../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/components/nuxt-error-boundary.vue")['default']>
export const LazyClientOnly: LazyComponent<typeof import("../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/components/client-only")['default']>
export const LazyDevOnly: LazyComponent<typeof import("../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/components/dev-only")['default']>
export const LazyServerPlaceholder: LazyComponent<typeof import("../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/components/server-placeholder")['default']>
export const LazyNuxtLink: LazyComponent<typeof import("../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/components/nuxt-link")['default']>
export const LazyNuxtLoadingIndicator: LazyComponent<typeof import("../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/components/nuxt-loading-indicator")['default']>
export const LazyNuxtTime: LazyComponent<typeof import("../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/components/nuxt-time.vue")['default']>
export const LazyNuxtRouteAnnouncer: LazyComponent<typeof import("../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/components/nuxt-route-announcer")['default']>
export const LazyNuxtImg: LazyComponent<typeof import("../../../node_modules/.pnpm/@nuxt+image@1.10.0/node_modules/@nuxt/image/dist/runtime/components/NuxtImg.vue")['default']>
export const LazyNuxtPicture: LazyComponent<typeof import("../../../node_modules/.pnpm/@nuxt+image@1.10.0/node_modules/@nuxt/image/dist/runtime/components/NuxtPicture.vue")['default']>
export const LazyIcon: LazyComponent<typeof import("../../../node_modules/.pnpm/@nuxt+icon@1.14.0_vite@6.3.5_vue@3.5.17/node_modules/@nuxt/icon/dist/runtime/components/index")['default']>
export const LazyNuxtPage: LazyComponent<typeof import("../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/pages/runtime/page")['default']>
export const LazyNoScript: LazyComponent<typeof import("../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/head/runtime/components")['NoScript']>
export const LazyLink: LazyComponent<typeof import("../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/head/runtime/components")['Link']>
export const LazyBase: LazyComponent<typeof import("../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/head/runtime/components")['Base']>
export const LazyTitle: LazyComponent<typeof import("../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/head/runtime/components")['Title']>
export const LazyMeta: LazyComponent<typeof import("../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/head/runtime/components")['Meta']>
export const LazyStyle: LazyComponent<typeof import("../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/head/runtime/components")['Style']>
export const LazyHead: LazyComponent<typeof import("../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/head/runtime/components")['Head']>
export const LazyHtml: LazyComponent<typeof import("../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/head/runtime/components")['Html']>
export const LazyBody: LazyComponent<typeof import("../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/head/runtime/components")['Body']>
export const LazyNuxtIsland: LazyComponent<typeof import("../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/components/nuxt-island")['default']>
export const LazyNuxtRouteAnnouncer: LazyComponent<IslandComponent<typeof import("../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/app/components/server-placeholder")['default']>>

export const componentNames: string[]
