{"id": "#", "properties": {"appConfig": {"id": "#appConfig", "properties": {"icon": {"title": "<PERSON><PERSON>t Icon", "description": "Configure Nuxt Icon module preferences.", "tags": ["@studioIcon material-symbols:star"], "id": "#appConfig/icon", "properties": {"size": {"title": "Icon Size", "description": "Set the default icon size.", "tags": ["@studioIcon material-symbols:format-size-rounded"], "tsType": "string | undefined", "id": "#appConfig/icon/size", "default": {}, "type": "any"}, "class": {"title": "CSS Class", "description": "Set the default CSS class.", "tags": ["@studioIcon material-symbols:css"], "id": "#appConfig/icon/class", "default": "", "type": "string"}, "attrs": {"title": "Default Attributes", "description": "Attributes applied to every icon component.\n\n@default { \"aria-hidden\": true }", "tags": ["@studioIcon material-symbols:settings"], "tsType": "Record<string, string | number | boolean>", "id": "#appConfig/icon/attrs", "default": {"aria-hidden": true}, "type": "object"}, "mode": {"title": "Default Rendering Mode", "description": "Set the default rendering mode for the icon component", "enum": ["css", "svg"], "tags": ["@studioIcon material-symbols:move-down-rounded"], "id": "#appConfig/icon/mode", "default": "css", "type": "string"}, "aliases": {"title": "Icon aliases", "description": "Define Icon aliases to update them easily without code changes.", "tags": ["@studioIcon material-symbols:star-rounded"], "tsType": "{ [alias: string]: string }", "id": "#appConfig/icon/aliases", "default": {}, "type": "object"}, "cssSelectorPrefix": {"title": "CSS Selector Prefix", "description": "Set the default CSS selector prefix.", "tags": ["@studioIcon material-symbols:format-textdirection-l-to-r"], "id": "#appConfig/icon/cssSelectorPrefix", "default": "i-", "type": "string"}, "cssLayer": {"title": "CSS Layer Name", "description": "Set the default CSS `@layer` name.", "tags": ["@studioIcon material-symbols:layers"], "tsType": "string | undefined", "id": "#appConfig/icon/cssLayer", "default": {}, "type": "any"}, "cssWherePseudo": {"title": "Use CSS `:where()` P<PERSON>udo Selector", "description": "Use CSS `:where()` pseudo selector to reduce specificity.", "tags": ["@studioIcon material-symbols:low-priority"], "id": "#appConfig/icon/cssWherePseudo", "default": true, "type": "boolean"}, "collections": {"title": "Icon Collections", "description": "List of known icon collections name. Used to resolve collection name ambiguity.\ne.g. `simple-icons-github` -> `simple-icons:github` instead of `simple:icons-github`\n\nWhen not provided, will use the full Iconify collection list.", "tags": ["@studioIcon material-symbols:format-list-bulleted"], "tsType": "string[] | null", "id": "#appConfig/icon/collections", "default": null, "type": "any"}, "customCollections": {"title": "Custom Icon Collections", "tags": ["@studioIcon material-symbols:format-list-bulleted"], "tsType": "string[] | null", "id": "#appConfig/icon/customCollections", "default": null, "type": "any"}, "provider": {"title": "Icon Provider", "description": "Provider to use for fetching icons\n\n- `server` - Fetch icons with a server handler\n- `iconify` - Fetch icons with Iconify API, purely client-side\n- `none` - Do not fetch icons (use client bundle only)\n\n`server` by default; `iconify` when `ssr: false`", "enum": ["server", "iconify", "none"], "tags": ["@studioIcon material-symbols:cloud"], "type": "\"server\" | \"iconify\" | undefined", "id": "#appConfig/icon/provider"}, "iconifyApiEndpoint": {"title": "Iconify API Endpoint URL", "description": "Define a custom Iconify API endpoint URL. Useful if you want to use a self-hosted Iconify API. Learn more: https://iconify.design/docs/api.", "tags": ["@studioIcon material-symbols:api"], "id": "#appConfig/icon/iconifyApiEndpoint", "default": "https://api.iconify.design", "type": "string"}, "fallbackToApi": {"title": "Fallback to Iconify API", "description": "Fallback to Iconify API if server provider fails to found the collection.", "tags": ["@studioIcon material-symbols:public"], "enum": [true, false, "server-only", "client-only"], "type": "boolean | \"server-only\" | \"client-only\"", "id": "#appConfig/icon/fallbackToApi", "default": true}, "localApiEndpoint": {"title": "Local API Endpoint Path", "description": "Define a custom path for the local API endpoint.", "tags": ["@studioIcon material-symbols:api"], "id": "#appConfig/icon/localApiEndpoint", "default": "/api/_nuxt_icon", "type": "string"}, "fetchTimeout": {"title": "Fetch Timeout", "description": "Set the timeout for fetching icons.", "tags": ["@studioIcon material-symbols:timer"], "id": "#appConfig/icon/fetchTimeout", "default": 1500, "type": "number"}, "customize": {"title": "Customize callback", "description": "Customize icon content (replace stroke-width, colors, etc...).", "tags": ["@studioIcon material-symbols:edit"], "type": "IconifyIconCustomizeCallback", "id": "#appConfig/icon/customize"}}, "type": "object", "default": {"size": {}, "class": "", "attrs": {"aria-hidden": true}, "mode": "css", "aliases": {}, "cssSelectorPrefix": "i-", "cssLayer": {}, "cssWherePseudo": true, "collections": null, "customCollections": null, "iconifyApiEndpoint": "https://api.iconify.design", "fallbackToApi": true, "localApiEndpoint": "/api/_nuxt_icon", "fetchTimeout": 1500}}}, "type": "object", "default": {"icon": {"size": {}, "class": "", "attrs": {"aria-hidden": true}, "mode": "css", "aliases": {}, "cssSelectorPrefix": "i-", "cssLayer": {}, "cssWherePseudo": true, "collections": null, "customCollections": null, "iconifyApiEndpoint": "https://api.iconify.design", "fallbackToApi": true, "localApiEndpoint": "/api/_nuxt_icon", "fetchTimeout": 1500}}}}, "type": "object", "default": {"appConfig": {"icon": {"size": {}, "class": "", "attrs": {"aria-hidden": true}, "mode": "css", "aliases": {}, "cssSelectorPrefix": "i-", "cssLayer": {}, "cssWherePseudo": true, "collections": null, "customCollections": null, "iconifyApiEndpoint": "https://api.iconify.design", "fallbackToApi": true, "localApiEndpoint": "/api/_nuxt_icon", "fetchTimeout": 1500}}}}