// ESLint config generated by Nuxt
/// <reference path="./eslint-typegen.d.ts" />
/* eslint-disable */
// @ts-nocheck

import typegen from '../../../node_modules/.pnpm/eslint-typegen@2.2.1_eslint@9.30.1/node_modules/eslint-typegen/dist/index.mjs';
import { createConfigForNuxt, defineFlatConfigs, resolveOptions } from '../../../node_modules/.pnpm/@nuxt+eslint-config@1.4.1_@vue+compiler-sfc@3.5.17_eslint@9.30.1_typescript@5.3.3/node_modules/@nuxt/eslint-config/dist/flat.mjs';
import { fileURLToPath } from 'node:url';

const r = (...args) => fileURLToPath(new URL(...args, import.meta.url))

export { defineFlatConfigs }

export const options = resolveOptions({
  features: {
  "standalone": true
},
  dirs: {
    pages: ["pages"],
    composables: ["composables", "utils"],
    components: ["components"],
    componentsPrefixed: [],
    layouts: ["layouts"],
    plugins: ["plugins"],
    middleware: ["middleware"],
    modules: ["modules"],
    servers: [],
    root: [],
    src: [""],
}
})

export const configs = createConfigForNuxt(options)

configs.append(
// Set globals from imports registry
{
  name: 'nuxt/import-globals',
  languageOptions: {
    globals: Object.fromEntries(["useNuxtDevTools","useImage","acceptHMRUpdate","defineStore","storeToRefs","usePinia","useGtag","useTrackEvent","defineAppConfig","__buildAssetsURL","__publicAssetsURL","definePageMeta","useAnalytics","Actor","ApiResponse","Category","ChatHistoryResponse","CheckInCoinsPerDay","CheckinResponse","CheckoutCreateParams","CheckoutCreateResponse","ClaimCheckinResponse","CompleteTaskResponse","InviteCodeResponse","PriceExtra","PriceItem","PriceListResponse","StoriesResponse","Story","StoryCategoriesResponse","SubCategory","SysConfigListResponse","TaskData","TaskItem","TasksResponse","useApi","BrandingConfig","useBranding","useCdn","DataContext","StoriesData","useDataManager","detectDeviceFromUserAgent","useDeviceDetection","useFilterSeo","GameStructuredData","useGameStructuredData","useMenuHeight","clearMessageInstance","message","MessageOptions","MessageType","setMessageInstance","useMessage","PageSeoConfig","usePageSeo","usePaymentConfig","useStories","EnhancedStory","StoriesContentResponse","StoryContent","useStoriesEnhancer","useStoriesV2","useTagsFilter","TokenStatus","TokenUserInfo","UserType","useTokenManager","useCheckinStore","useLoadingStore","useRechargeStore","useStoryStore","useSysConfigStore","useTasksStore","useThemeStore","useUserStore","debounce","delay","formatTimestamp","generateUUID","isAndroid","isIOS","isMobile","JSONParse","throttle","cancelIdleCallback","requestIdleCallback","setInterval","defineNuxtLink","clearNuxtData","refreshNuxtData","useAsyncData","useLazyAsyncData","useNuxtData","reloadNuxtApp","defineNuxtComponent","refreshCookie","useCookie","clearError","createError","isNuxtError","showError","useError","useFetch","useLazyFetch","injectHead","useHead","useHeadSafe","useSeoMeta","useServerHead","useServerHeadSafe","useServerSeoMeta","useHydration","useLoadingIndicator","getAppManifest","getRouteRules","callOnce","definePayloadReducer","definePayloadReviver","isPrerendered","loadPayload","preloadPayload","prefetchComponents","preloadComponents","preloadRouteComponents","usePreviewMode","onNuxtReady","useRouteAnnouncer","abortNavigation","addRouteMiddleware","defineNuxtRouteMiddleware","navigateTo","setPageLayout","useRoute","useRouter","useRuntimeHook","useScript","useScriptClarity","useScriptCloudflareWebAnalytics","useScriptCrisp","useScriptEventPage","useScriptFathomAnalytics","useScriptGoogleAdsense","useScriptGoogleAnalytics","useScriptGoogleMaps","useScriptGoogleTagManager","useScriptHotjar","useScriptIntercom","useScriptLemonSqueezy","useScriptMatomoAnalytics","useScriptMetaPixel","useScriptNpm","useScriptPlausibleAnalytics","useScriptRybbitAnalytics","useScriptSegment","useScriptSnapchatPixel","useScriptStripe","useScriptTriggerConsent","useScriptTriggerElement","useScriptUmamiAnalytics","useScriptVimeoPlayer","useScriptXPixel","useScriptYouTubePlayer","onPrehydrate","prerenderRoutes","setResponseStatus","useRequestEvent","useRequestFetch","useRequestHeader","useRequestHeaders","useResponseHeader","clearNuxtState","useState","useRequestURL","updateAppConfig","useAppConfig","defineAppConfig","defineNuxtPlugin","definePayloadPlugin","tryUseNuxtApp","useNuxtApp","useRuntimeConfig","appendCorsHeaders","appendCorsPreflightHeaders","appendHeader","appendHeaders","appendResponseHeader","appendResponseHeaders","assertMethod","callNodeListener","clearResponseHeaders","clearSession","createApp","createAppEventHandler","createError","createEvent","createEventStream","createRouter","defaultContentType","defineEventHandler","defineLazyEventHandler","defineNodeListener","defineNodeMiddleware","defineRequestMiddleware","defineResponseMiddleware","defineWebSocket","defineWebSocketHandler","deleteCookie","dynamicEventHandler","eventHandler","fetchWithEvent","fromNodeMiddleware","fromPlainHandler","fromWebHandler","getCookie","getHeader","getHeaders","getMethod","getProxyRequestHeaders","getQuery","getRequestFingerprint","getRequestHeader","getRequestHeaders","getRequestHost","getRequestIP","getRequestPath","getRequestProtocol","getRequestURL","getRequestWebStream","getResponseHeader","getResponseHeaders","getResponseStatus","getResponseStatusText","getRouterParam","getRouterParams","getSession","getValidatedQuery","getValidatedRouterParams","handleCacheHeaders","handleCors","isCorsOriginAllowed","isError","isEvent","isEventHandler","isMethod","isPreflightRequest","isStream","isWebResponse","lazyEventHandler","parseCookies","promisifyNodeListener","proxyRequest","readBody","readFormData","readMultipartFormData","readRawBody","readValidatedBody","removeResponseHeader","sanitizeStatusCode","sanitizeStatusMessage","sealSession","send","sendError","sendIterable","sendNoContent","sendProxy","sendRedirect","sendStream","sendWebResponse","serveStatic","setCookie","setHeader","setHeaders","setResponseHeader","setResponseHeaders","setResponseStatus","splitCookiesString","toEventHandler","toNodeListener","toPlainHandler","toWebHandler","toWebRequest","unsealSession","updateSession","useBase","useSession","writeEarlyHints","useNitroApp","cachedEventHandler","cachedFunction","defineCachedEventHandler","defineCachedFunction","useAppConfig","useRuntimeConfig","useEvent","defineNitroErrorHandler","defineRouteMeta","defineNitroPlugin","nitroPlugin","defineRenderHandler","getRouteRules","useStorage","defineTask","runTask","Component","ComponentPublicInstance","computed","ComputedRef","customRef","defineAsyncComponent","defineComponent","DirectiveBinding","effect","effectScope","ExtractDefaultPropTypes","ExtractPropTypes","ExtractPublicPropTypes","getCurrentInstance","getCurrentScope","h","hasInjectionContext","inject","InjectionKey","isProxy","isReactive","isReadonly","isRef","isShallow","markRaw","MaybeRef","MaybeRefOrGetter","mergeModels","nextTick","onActivated","onBeforeMount","onBeforeUnmount","onBeforeUpdate","onDeactivated","onErrorCaptured","onMounted","onRenderTracked","onRenderTriggered","onScopeDispose","onServerPrefetch","onUnmounted","onUpdated","PropType","provide","proxyRefs","reactive","readonly","ref","Ref","resolveComponent","shallowReactive","shallowReadonly","shallowRef","toRaw","toRef","toRefs","toValue","triggerRef","unref","useAttrs","useCssModule","useCssVars","useId","useModel","useShadowRoot","useSlots","useTemplateRef","useTransitionState","VNode","watch","watchEffect","watchPostEffect","watchSyncEffect","withCtx","withDirectives","withKeys","withMemo","withModifiers","withScopeId","WritableComputedRef","isVue2","isVue3","onBeforeRouteLeave","onBeforeRouteUpdate","useLink"].map(i => [i, 'readonly'])),
  },
}
)

export function withNuxt(...customs) {
  return configs
    .clone()
    .append(...customs)
    .onResolved(configs => typegen(configs, { dtsPath: r("./eslint-typegen.d.ts"), augmentFlatConfigUtils: true }))
}

export default withNuxt