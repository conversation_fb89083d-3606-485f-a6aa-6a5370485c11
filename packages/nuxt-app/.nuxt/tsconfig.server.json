{"compilerOptions": {"forceConsistentCasingInFileNames": true, "strict": true, "noEmit": true, "skipLibCheck": true, "target": "ESNext", "module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "allowJs": true, "resolveJsonModule": true, "jsx": "preserve", "allowSyntheticDefaultImports": true, "jsxFactory": "h", "jsxFragmentFactory": "Fragment", "paths": {"#imports": ["./types/nitro-imports"], "~/*": ["../*"], "@/*": ["../*"], "~~/*": ["../*"], "@@/*": ["../*"], "nitropack/types": ["../../../node_modules/.pnpm/nitropack@2.11.13/node_modules/nitropack/types"], "nitropack/runtime": ["../../../node_modules/.pnpm/nitropack@2.11.13/node_modules/nitropack/runtime"], "nitropack": ["../../../node_modules/.pnpm/nitropack@2.11.13/node_modules/nitropack"], "defu": ["../../../node_modules/.pnpm/defu@6.1.4/node_modules/defu"], "h3": ["../../../node_modules/.pnpm/h3@1.15.3/node_modules/h3"], "consola": ["../../../node_modules/.pnpm/consola@3.4.2/node_modules/consola"], "ofetch": ["../../../node_modules/.pnpm/ofetch@1.4.1/node_modules/ofetch"], "@nuxt/devtools": ["../../../node_modules/.pnpm/@nuxt+devtools@2.6.2_vite@6.3.5_vue@3.5.17/node_modules/@nuxt/devtools"], "@vue/runtime-core": ["../../../node_modules/.pnpm/@vue+runtime-core@3.5.17/node_modules/@vue/runtime-core"], "@vue/compiler-sfc": ["../../../node_modules/.pnpm/@vue+compiler-sfc@3.5.17/node_modules/@vue/compiler-sfc"], "unplugin-vue-router/client": ["../../../node_modules/.pnpm/unplugin-vue-router@0.14.0_@vue+compiler-sfc@3.5.17_vue-router@4.5.1_vue@3.5.17/node_modules/unplugin-vue-router/client"], "@nuxt/schema": ["../../../node_modules/.pnpm/@nuxt+schema@3.17.6/node_modules/@nuxt/schema"], "nuxt": ["../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt"], "vite/client": ["../../../node_modules/.pnpm/vite@6.3.5_@types+node@20.19.4_jiti@2.4.2_less@4.3.0_terser@5.43.1/node_modules/vite/client"], "#shared": ["../shared"], "assets": ["../assets"], "assets/*": ["../assets/*"], "public": ["../public"], "public/*": ["../public/*"], "#build": ["./"], "#build/*": ["./*"], "#internal/nuxt/paths": ["../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/core/runtime/nitro/utils/paths"], "#image": ["../../../node_modules/.pnpm/@nuxt+image@1.10.0/node_modules/@nuxt/image/dist/runtime"], "#image/*": ["../../../node_modules/.pnpm/@nuxt+image@1.10.0/node_modules/@nuxt/image/dist/runtime/*"], "#unhead/composables": ["../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/dist/head/runtime/composables/v3"], "#nuxt-icon-server-bundle": ["./nuxt-icon-server-bundle"]}, "lib": ["esnext", "webworker", "dom.iterable"]}, "include": ["./types/nitro-nuxt.d.ts", "../../../node_modules/.pnpm/@nuxt+eslint@1.4.1_@vue+compiler-sfc@3.5.17_eslint@9.30.1_typescript@5.3.3_vite@6.3.5/node_modules/@nuxt/eslint/runtime/server", "../../../node_modules/.pnpm/@nuxt+fonts@0.11.4_vite@6.3.5/node_modules/@nuxt/fonts/runtime/server", "../../../node_modules/.pnpm/@nuxt+icon@1.14.0_vite@6.3.5_vue@3.5.17/node_modules/@nuxt/icon/runtime/server", "../../../node_modules/.pnpm/@nuxt+image@1.10.0/node_modules/@nuxt/image/runtime/server", "../../../node_modules/.pnpm/@pinia+nuxt@0.11.1_pinia@3.0.3/node_modules/@pinia/nuxt/runtime/server", "../../../node_modules/.pnpm/@vueuse+nuxt@11.3.0_nuxt@3.17.6_vue@3.5.17/node_modules/@vueuse/nuxt/runtime/server", "../../../node_modules/.pnpm/nuxt-gtag@3.0.3/node_modules/nuxt-gtag/runtime/server", "../../../node_modules/.pnpm/@nuxt+devtools@2.6.2_vite@6.3.5_vue@3.5.17/node_modules/@nuxt/devtools/runtime/server", "../../../node_modules/.pnpm/@nuxt+telemetry@2.6.6/node_modules/@nuxt/telemetry/runtime/server", "./types/nitro.d.ts", "../**/*", "../server/**/*"], "exclude": ["../node_modules", "../../../node_modules", "../../../node_modules/.pnpm/nuxt@3.17.6_@types+node@20.19.4_@vue+compiler-sfc@3.5.17_eslint@9.30.1_less@4.3.0_terser@5.43_l3zm7ss5hvjqq24du2irbpwvf4/node_modules/nuxt/node_modules", "../../../node_modules/.pnpm/@nuxt+eslint@1.4.1_@vue+compiler-sfc@3.5.17_eslint@9.30.1_typescript@5.3.3_vite@6.3.5/node_modules/@nuxt/eslint/node_modules", "../../../node_modules/.pnpm/@nuxt+fonts@0.11.4_vite@6.3.5/node_modules/@nuxt/fonts/node_modules", "../../../node_modules/.pnpm/@nuxt+icon@1.14.0_vite@6.3.5_vue@3.5.17/node_modules/@nuxt/icon/node_modules", "../../../node_modules/.pnpm/@nuxt+image@1.10.0/node_modules/@nuxt/image/node_modules", "../../../node_modules/.pnpm/@pinia+nuxt@0.11.1_pinia@3.0.3/node_modules/@pinia/nuxt/node_modules", "../../../node_modules/.pnpm/@vueuse+nuxt@11.3.0_nuxt@3.17.6_vue@3.5.17/node_modules/@vueuse/nuxt/node_modules", "../../../node_modules/.pnpm/nuxt-gtag@3.0.3/node_modules/nuxt-gtag/node_modules", "../../../node_modules/.pnpm/@nuxt+devtools@2.6.2_vite@6.3.5_vue@3.5.17/node_modules/@nuxt/devtools/node_modules", "../../../node_modules/.pnpm/@nuxt+telemetry@2.6.6/node_modules/@nuxt/telemetry/node_modules", "../dist"]}