import { defineStore } from 'pinia'
import { ref, readonly } from 'vue'
import {
  createPaymentAPI,
  type PriceItem,
  getProjectConfig,
  isPlayshot,
} from 'shared-payment'
import { getDynamicApiHost } from '@/utils/dynamicApiHost'

export const useRechargeStore = defineStore(
  'recharge',
  () => {
    // 获取支付提供商调试参数
    const debugPaymentProvider = import.meta.env.VITE_PAYMENT_PROVIDER as string

    // 获取 Stripe 公钥（从根目录的环境变量）
    const stripePublicKey = import.meta.env.VITE_STRIPE_PUBLIC_KEY as string

    // 创建支付API实例，传递调试参数和 Stripe 公钥
    const paymentAPI = createPaymentAPI(
      getDynamicApiHost(),
      () => localStorage.getItem('token') || '',
      {
        debugPaymentProvider,
        stripePublicKey,
      },
    )

    // State
    const visible = ref(false)
    const priceList = ref<PriceItem[]>([])
    const loading = ref(false)
    const error = ref<string | null>(null)
    const paymentLoading = ref(false)

    // 获取项目配置
    const projectConfig = getProjectConfig()

    // Actions
    const fetchPriceList = async () => {
      loading.value = true
      error.value = null

      try {
        const data = await paymentAPI.getPriceList()
        priceList.value = data
      } catch (err) {
        error.value =
          err instanceof Error ? err.message : 'Failed to fetch price list'
        throw err
      } finally {
        loading.value = false
      }
    }

    /**
     * 创建支付并跳转
     */
    const createAndRedirectToPayment = async (
      priceId: string,
    ): Promise<void> => {
      // 直接调用 shared-payment，内部已处理所有错误逻辑
      await paymentAPI.createAndRedirectToPayment({
        priceId,
        successUrl: generateSuccessUrl(),
        cancelUrl: generateCancelUrl(),
      })
    }

    const showRechargeModal = () => {
      visible.value = true
    }

    const hideRechargeModal = () => {
      visible.value = false
    }

    const toggleRechargeModal = () => {
      visible.value = !visible.value
    }

    const reset = () => {
      priceList.value = []
      error.value = null
      paymentLoading.value = false
    }

    // 生成成功回调URL
    const generateSuccessUrl = (): string => {
      if (typeof window !== 'undefined') {
        const baseUrl = window.location.origin

        if (isPlayshot()) {
          return `${baseUrl}/recharge-success`
        } else {
          return `${baseUrl}/payment/stripe-callback`
        }
      }
      return ''
    }

    // 生成取消回调URL
    const generateCancelUrl = (): string => {
      if (typeof window !== 'undefined') {
        return window.location.href
      }
      return ''
    }

    return {
      // State
      visible,
      priceList,
      loading,
      error,
      paymentLoading,

      // Computed
      projectConfig: readonly(ref(projectConfig)),

      // Actions
      fetchPriceList,
      createAndRedirectToPayment,
      showRechargeModal,
      hideRechargeModal,
      toggleRechargeModal,
      reset,
    }
  },
  {
    persist: false,
  },
)
