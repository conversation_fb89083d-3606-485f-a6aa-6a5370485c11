# 开发/测试环境配置
VITE_API_HOST=https://api-test.zhijianyuzhou.com
VITE_APP_TITLE=PlayShot.AI - Development
VITE_APP_ENV=development

# 微前端配置
# 本地开发时使用 localhost，部署到测试环境时使用域名
VITE_MAIN_APP_URL=http://localhost:3000
VITE_CSR_APP_URL=http://localhost:5173

# 调试配置
VITE_DEBUG=true
VITE_ENABLE_MOCK=false

# 支付提供商配置 (本地开发环境统一使用 stripe)
VITE_PAYMENT_PROVIDER=stripe
VITE_STRIPE_PUBLIC_KEY=pk_test_51QU24xG7ZqVosOUplhEMVx9BLgirhPhUfAIyh9DTCEEoB4nYzS2gZZu4l76FM14wSJlDERe0oxB5iRSgyrHJKAc500edtUOUeF

# 品牌配置
VITE_APP_NAME=PlayShot
VITE_WEBSITE_TITLE=PlayShot
VITE_LOGO_URL=https://static.playshot.ai/static/images/logo/playshot_logo.png
VITE_ICON_URL=https://cdn.magiclight.ai/assets/playshot/playshot-icon.png
