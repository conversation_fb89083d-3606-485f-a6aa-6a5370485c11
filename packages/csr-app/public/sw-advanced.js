/**
 * 高级Service Worker
 * 实现激进缓存策略、预缓存、后台同步等功能
 */

const CACHE_NAME = 'playshot-v1.3.1'
const STATIC_CACHE = 'playshot-static-v1.3.1'
const DYNAMIC_CACHE = 'playshot-dynamic-v1.3.1'
const CDN_CACHE = 'playshot-cdn-v1.3.1'

// 需要预缓存的关键资源
const PRECACHE_URLS = [
  '/',
  '/manifest.json',
  // 关键CSS和JS将在安装时动态添加
]

// CDN资源缓存策略
const CDN_PATTERNS = [
  /^https:\/\/cdn\.jsdelivr\.net/,
  /^https:\/\/unpkg\.com/,
  /^https:\/\/fonts\.googleapis\.com/,
  /^https:\/\/fonts\.gstatic\.com/,
  /^https:\/\/cdn\.magiclight\.ai/,
  /^https:\/\/static\.playshot\.ai/
]

// API缓存策略
const API_PATTERNS = [
  /^https:\/\/api\.reelplay\.ai/,
  /^https:\/\/api\.playshot\.ai/
]

// 安装事件 - 预缓存关键资源
self.addEventListener('install', event => {
  console.log('SW: Installing...')
  
  event.waitUntil(
    Promise.all([
      // 预缓存静态资源
      caches.open(STATIC_CACHE).then(cache => {
        return cache.addAll(PRECACHE_URLS)
      }),
      
      // 预缓存CDN资源
      caches.open(CDN_CACHE).then(cache => {
        const cdnUrls = [
          'https://cdn.jsdelivr.net/npm/@arco-design/web-vue@2.44.7/dist/arco-vue.min.js',
          'https://cdn.jsdelivr.net/npm/@arco-design/web-vue@2.44.7/dist/arco.min.css',
          'https://cdn.jsdelivr.net/npm/lodash@4.17.21/lodash.min.js',
          'https://fonts.googleapis.com/css2?family=Work+Sans:wght@400;500;600&display=swap'
        ]
        return Promise.allSettled(
          cdnUrls.map(url => 
            fetch(url).then(response => {
              if (response.ok) {
                return cache.put(url, response)
              }
            }).catch(() => {
              // 静默失败，不阻塞安装
            })
          )
        )
      })
    ]).then(() => {
      console.log('SW: Installation complete')
      // 强制激活新的SW
      return self.skipWaiting()
    })
  )
})

// 激活事件 - 清理旧缓存
self.addEventListener('activate', event => {
  console.log('SW: Activating...')
  
  event.waitUntil(
    Promise.all([
      // 清理旧缓存
      caches.keys().then(cacheNames => {
        return Promise.all(
          cacheNames.map(cacheName => {
            if (cacheName !== CACHE_NAME && 
                cacheName !== STATIC_CACHE && 
                cacheName !== DYNAMIC_CACHE &&
                cacheName !== CDN_CACHE) {
              console.log('SW: Deleting old cache:', cacheName)
              return caches.delete(cacheName)
            }
          })
        )
      }),
      
      // 立即控制所有客户端
      self.clients.claim()
    ]).then(() => {
      console.log('SW: Activation complete')
    })
  )
})

// 获取事件 - 智能缓存策略
self.addEventListener('fetch', event => {
  const { request } = event
  const url = new URL(request.url)
  
  // 跳过非GET请求
  if (request.method !== 'GET') {
    return
  }
  
  // 跳过chrome-extension等特殊协议
  if (!url.protocol.startsWith('http')) {
    return
  }
  
  event.respondWith(handleRequest(request))
})

// 智能请求处理
async function handleRequest(request) {
  const url = new URL(request.url)
  
  try {
    // 1. CDN资源 - 缓存优先策略
    if (CDN_PATTERNS.some(pattern => pattern.test(url.href))) {
      return await handleCDNRequest(request)
    }
    
    // 2. API请求 - 网络优先策略
    if (API_PATTERNS.some(pattern => pattern.test(url.href))) {
      return await handleAPIRequest(request)
    }
    
    // 3. 静态资源 - 缓存优先策略
    if (isStaticResource(url)) {
      return await handleStaticRequest(request)
    }
    
    // 4. HTML页面 - 网络优先策略
    if (request.headers.get('accept')?.includes('text/html')) {
      return await handleHTMLRequest(request)
    }
    
    // 5. 其他资源 - 网络优先策略
    return await handleDynamicRequest(request)
    
  } catch (error) {
    console.error('SW: Request failed:', error)
    return await handleOfflineRequest(request)
  }
}

// CDN资源处理 - 缓存优先，长期缓存
async function handleCDNRequest(request) {
  const cache = await caches.open(CDN_CACHE)
  const cached = await cache.match(request)
  
  if (cached) {
    // 后台更新
    fetch(request).then(response => {
      if (response.ok) {
        cache.put(request, response.clone())
      }
    }).catch(() => {})
    
    return cached
  }
  
  const response = await fetch(request)
  if (response.ok) {
    cache.put(request, response.clone())
  }
  return response
}

// API请求处理 - 网络优先，短期缓存
async function handleAPIRequest(request) {
  const cache = await caches.open(DYNAMIC_CACHE)
  
  try {
    const response = await fetch(request)
    
    if (response.ok) {
      // 只缓存GET请求的成功响应
      if (request.method === 'GET') {
        // 设置较短的缓存时间
        const clonedResponse = response.clone()
        setTimeout(() => {
          cache.put(request, clonedResponse)
        }, 0)
      }
    }
    
    return response
  } catch (error) {
    // 网络失败时返回缓存
    const cached = await cache.match(request)
    if (cached) {
      return cached
    }
    throw error
  }
}

// 静态资源处理 - 缓存优先
async function handleStaticRequest(request) {
  const cache = await caches.open(STATIC_CACHE)
  const cached = await cache.match(request)
  
  if (cached) {
    return cached
  }
  
  const response = await fetch(request)
  if (response.ok) {
    cache.put(request, response.clone())
  }
  return response
}

// HTML页面处理 - 网络优先
async function handleHTMLRequest(request) {
  const cache = await caches.open(DYNAMIC_CACHE)
  
  try {
    const response = await fetch(request)
    if (response.ok) {
      cache.put(request, response.clone())
    }
    return response
  } catch (error) {
    const cached = await cache.match(request)
    if (cached) {
      return cached
    }
    
    // 返回离线页面
    return await cache.match('/') || new Response('Offline', { status: 503 })
  }
}

// 动态资源处理
async function handleDynamicRequest(request) {
  const cache = await caches.open(DYNAMIC_CACHE)
  
  try {
    const response = await fetch(request)
    if (response.ok) {
      cache.put(request, response.clone())
    }
    return response
  } catch (error) {
    const cached = await cache.match(request)
    if (cached) {
      return cached
    }
    throw error
  }
}

// 离线处理
async function handleOfflineRequest(request) {
  const cache = await caches.open(STATIC_CACHE)
  
  // 尝试返回缓存的资源
  const cached = await cache.match(request)
  if (cached) {
    return cached
  }
  
  // 返回离线页面或错误响应
  if (request.headers.get('accept')?.includes('text/html')) {
    return await cache.match('/') || new Response('Offline', { status: 503 })
  }
  
  return new Response('Resource not available offline', { status: 503 })
}

// 判断是否为静态资源
function isStaticResource(url) {
  const staticExtensions = ['.js', '.css', '.png', '.jpg', '.jpeg', '.gif', '.webp', '.svg', '.ico', '.woff', '.woff2', '.ttf', '.eot']
  return staticExtensions.some(ext => url.pathname.endsWith(ext))
}

// 消息处理
self.addEventListener('message', event => {
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting()
  }
  
  if (event.data && event.data.type === 'CACHE_URLS') {
    const urls = event.data.urls
    caches.open(STATIC_CACHE).then(cache => {
      return cache.addAll(urls)
    })
  }
})

// 后台同步
self.addEventListener('sync', event => {
  if (event.tag === 'background-sync') {
    event.waitUntil(doBackgroundSync())
  }
})

async function doBackgroundSync() {
  // 实现后台同步逻辑
  console.log('SW: Background sync triggered')
}
