// 🚀 Service Worker 调试版本
console.log('🚀 Service Worker starting...')

// 版本控制 - 与package.json保持同步
const VERSION = 'v1.3.1'
const BUILD_TIME = Date.now() // 构建时间戳，用于缓存破坏

// 🎯 导入 Workbox 模块
let workboxLoaded = false
try {
  importScripts('https://storage.googleapis.com/workbox-cdn/releases/7.3.0/workbox-sw.js')
  workboxLoaded = true
  console.log('✅ Workbox script loaded successfully')
} catch (error) {
  console.error('❌ Failed to load Workbox:', error)
  workboxLoaded = false
}

// 🎯 Workbox 核心配置
if (workboxLoaded && typeof workbox !== 'undefined' && workbox) {
  console.log('🚀 Workbox available - Professional PWA caching enabled')

  try {
    // 设置缓存名称前缀和版本
    workbox.core.setCacheNameDetails({
      prefix: 'magic-partner',
      suffix: VERSION,
      precache: 'precache',
      runtime: 'runtime'
    })

    // 启用 Workbox 日志（开发环境）
    if (self.location.hostname === 'localhost') {
      workbox.setConfig({ debug: false }) // 减少调试日志
    }

    // 跳过等待，立即激活新的 Service Worker
    workbox.core.skipWaiting()
    workbox.core.clientsClaim()

    console.log('✅ Workbox core configuration completed')
  } catch (error) {
    console.error('❌ Workbox configuration failed:', error)
    workboxLoaded = false
  }
} else {
  console.error('❌ Workbox not available - will use fallback')
  workboxLoaded = false
}

// 🎯 Workbox 预缓存配置 - 首屏优化
const PRECACHE_MANIFEST = [
  { url: '/', revision: VERSION },
  { url: '/manifest.json', revision: VERSION },
  // 关键字体已通过 Google Fonts 优化，无需在 SW 中预缓存
  // 关键图片 - 首屏必需
  {
    url: 'https://cdn.magiclight.ai/assets/playshot/logo-v2.png',
    revision: VERSION
  },
  // 首屏关键样式
  { url: '/src/assets/style/theme.less', revision: VERSION },
  { url: '/src/assets/style/global.less', revision: VERSION }
]

// 🚀 启用 Workbox 预缓存
if (typeof workbox !== 'undefined' && workbox && workbox.precaching) {
  try {
    // 预缓存静态资源
    workbox.precaching.precacheAndRoute(PRECACHE_MANIFEST)
    console.log('✅ Workbox precaching configured')
  } catch (error) {
    console.warn('⚠️ Workbox precaching failed:', error)
  }
} else {
  console.log('⚠️ Workbox precaching not available')
}

// 🎯 Workbox 路由策略配置
if (workboxLoaded && workbox && workbox.routing && workbox.strategies) {
  console.log('🚀 Configuring Workbox routing strategies...')

  try {
    // 📦 带版本控制的静态资源 - CacheFirst 策略（长期缓存）
    workbox.routing.registerRoute(
      ({ request, url }) => {
        const isVersionedAsset = url.pathname.includes('/assets-v')
        const isStaticResource =
          request.destination === 'script' ||
          request.destination === 'style' ||
          request.destination === 'font'

        return isVersionedAsset && isStaticResource
      },
      new workbox.strategies.CacheFirst({
        cacheName: 'versioned-static-resources',
        plugins: [
          new workbox.cacheableResponse.CacheableResponsePlugin({
            statuses: [0, 200]
          }),
          new workbox.expiration.ExpirationPlugin({
            maxEntries: 200,
            maxAgeSeconds: 365 * 24 * 60 * 60 // 1年，因为有版本控制
          }),
          {
            cacheWillUpdate: async ({ response }) => {
              console.log('💾 缓存版本化静态资源')
              return response.status === 200 ? response : null
            }
          }
        ]
      })
    )

    // 📦 非版本化静态资源 - NetworkFirst 策略（短期缓存）
    workbox.routing.registerRoute(
      ({ request, url }) => {
        const isVersionedAsset = url.pathname.includes('/assets-v')
        const isStaticResource =
          request.destination === 'script' ||
          request.destination === 'style' ||
          request.destination === 'font'

        return !isVersionedAsset && isStaticResource
      },
      new workbox.strategies.NetworkFirst({
        cacheName: 'non-versioned-static-resources',
        networkTimeoutSeconds: 3,
        plugins: [
          new workbox.cacheableResponse.CacheableResponsePlugin({
            statuses: [0, 200]
          }),
          new workbox.expiration.ExpirationPlugin({
            maxEntries: 50,
            maxAgeSeconds: 60 * 60 // 1小时
          })
        ]
      })
    )

    // 🖼️ 带版本控制的图片资源 - CacheFirst 策略（长期缓存）
    workbox.routing.registerRoute(
      ({ request, url }) => {
        const isVersionedAsset = url.pathname.includes('/assets-v')
        const isImage = request.destination === 'image'

        return isVersionedAsset && isImage
      },
      new workbox.strategies.CacheFirst({
        cacheName: 'versioned-images',
        plugins: [
          new workbox.cacheableResponse.CacheableResponsePlugin({
            statuses: [0, 200]
          }),
          new workbox.expiration.ExpirationPlugin({
            maxEntries: 300,
            maxAgeSeconds: 365 * 24 * 60 * 60 // 1年，因为有版本控制
          })
        ]
      })
    )

    // 🖼️ 非版本化图片资源 - StaleWhileRevalidate 策略
    workbox.routing.registerRoute(
      ({ request, url }) => {
        const isVersionedAsset = url.pathname.includes('/assets-v')
        const isImage = request.destination === 'image'

        return !isVersionedAsset && isImage
      },
      new workbox.strategies.StaleWhileRevalidate({
        cacheName: 'non-versioned-images',
        plugins: [
          new workbox.cacheableResponse.CacheableResponsePlugin({
            statuses: [0, 200]
          }),
          new workbox.expiration.ExpirationPlugin({
            maxEntries: 100,
            maxAgeSeconds: 24 * 60 * 60 // 1天
          })
        ]
      })
    )

    // 🌐 API 缓存策略 - 分类处理不同类型的 API
    console.log('🔧 Registering API routes...')

    // 🚀 首屏关键 API - StaleWhileRevalidate 策略 (快速响应)
    workbox.routing.registerRoute(
      ({ url }) => {
        const isFirstScreenApi =
          url.pathname.startsWith('/api/v1/story.list') ||
          url.pathname.startsWith('/api/v1/sysconfig.list') ||
          url.pathname.startsWith('/api/v1/actor.list')

        return isFirstScreenApi
      },
      new workbox.strategies.StaleWhileRevalidate({
        cacheName: 'api-first-screen',
        plugins: [
          new workbox.cacheableResponse.CacheableResponsePlugin({
            statuses: [0, 200]
          }),
          new workbox.expiration.ExpirationPlugin({
            maxEntries: 20,
            maxAgeSeconds: 10 * 60 // 10分钟，保持相对新鲜
          }),
          {
            cacheWillUpdate: async ({ response }) => {
              console.log('💾 缓存首屏 API 响应')
              return response.status === 200 ? response : null
            },
            cachedResponseWillBeUsed: async ({ cachedResponse }) => {
              if (cachedResponse) {
                console.log('⚡ 首屏 API 缓存命中 - 极速加载')
              }
              return cachedResponse
            }
          }
        ]
      })
    )

    // 📊 静态配置 API - CacheFirst 策略 (24小时)
    workbox.routing.registerRoute(
      ({ url }) => {
        const isApiMatch =
          url.pathname.startsWith('/api/v1/story-template.list') ||
          url.pathname.startsWith('/api/v1/talent/skill-info.list')

        return isApiMatch
      },
      new workbox.strategies.CacheFirst({
        cacheName: 'api-static',
        plugins: [
          new workbox.cacheableResponse.CacheableResponsePlugin({
            statuses: [0, 200]
          }),
          new workbox.expiration.ExpirationPlugin({
            maxEntries: 50,
            maxAgeSeconds: 24 * 60 * 60 // 24小时
          })
        ]
      })
    )

    // 🔄 动态数据 API - NetworkFirst 策略 (5分钟)
    workbox.routing.registerRoute(
      ({ url }) =>
        url.pathname.startsWith('/api/v1/story.list') ||
        url.pathname.startsWith('/api/v1/user.whoami') ||
        url.pathname.startsWith('/api/v1/game-history.list') ||
        url.pathname.startsWith('/api/v1/favorite.list') ||
        url.pathname.startsWith('/api/v1/daily-tasks.list'),
      new workbox.strategies.NetworkFirst({
        cacheName: 'api-dynamic',
        networkTimeoutSeconds: 5,
        plugins: [
          new workbox.cacheableResponse.CacheableResponsePlugin({
            statuses: [0, 200]
          }),
          new workbox.expiration.ExpirationPlugin({
            maxEntries: 100,
            maxAgeSeconds: 5 * 60 // 5分钟
          })
        ]
      })
    )

    // 🚫 敏感操作 API - NetworkOnly 策略 (不缓存)
    workbox.routing.registerRoute(
      ({ url }) =>
        url.pathname.startsWith('/api/v1/user-guest.sign-up') ||
        url.pathname.startsWith('/api/v1/user.auth') ||
        url.pathname.startsWith('/api/v1/verify-code.send') ||
        url.pathname.startsWith('/api/v1/favorite.create') ||
        url.pathname.startsWith('/api/v1/daily-tasks.play-game'),
      new workbox.strategies.NetworkOnly()
    )

    // 🎬 视频缓存策略 - 专业处理视频资源
    workbox.routing.registerRoute(
      ({ request, url }) =>
        request.destination === 'video' || url.pathname.match(/\.(mp4|webm|ogg|mov)$/i),
      new workbox.strategies.CacheFirst({
        cacheName: 'videos',
        plugins: [
          new workbox.cacheableResponse.CacheableResponsePlugin({
            statuses: [0, 200, 206] // 支持范围请求
          }),
          new workbox.expiration.ExpirationPlugin({
            maxEntries: 50,
            maxAgeSeconds: 7 * 24 * 60 * 60, // 7天
            purgeOnQuotaError: true // 存储空间不足时自动清理
          }),
          {
            // 自定义插件：处理Range请求
            cacheKeyWillBeUsed: async ({ request }) => {
              // 移除Range header以统一缓存键
              const url = new URL(request.url)
              return new Request(url.href, {
                method: 'GET',
                headers: new Headers()
              })
            },
            cacheWillUpdate: async ({ response }) => {
              // 只缓存完整的视频文件
              return response.status === 200 ? response : null
            },
            cachedResponseWillBeUsed: async ({ cachedResponse, request }) => {
              if (cachedResponse) {
                console.log('✅ Workbox video cache hit:', request.url)
              }
              return cachedResponse
            }
          }
        ]
      })
    )

    // 📄 HTML 页面 - NetworkOnly 策略（永不缓存，确保总是最新）
    workbox.routing.registerRoute(
      ({ request }) => request.destination === 'document',
      new workbox.strategies.NetworkOnly()
    )

    console.log('✅ All Workbox caching strategies configured')
  } catch (error) {
    console.error('❌ Failed to configure Workbox routing strategies:', error)
  }
} else {
  console.log('⚠️ Workbox routing not available')
}

// 🧹 增强的缓存清理机制
self.addEventListener('activate', (event) => {
  console.log('🚀 Service Worker 激活，开始清理旧缓存...')

  event.waitUntil(
    Promise.all([
      // 清理所有缓存
      caches.keys().then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            // 清理不匹配当前版本的缓存
            if (!cacheName.includes(VERSION)) {
              console.log('🗑️ 删除旧版本缓存:', cacheName)
              return caches.delete(cacheName)
            }

            // 清理过期的缓存（超过7天的非版本化缓存）
            if (
              !cacheName.includes('versioned') &&
              (cacheName.includes('static') || cacheName.includes('images'))
            ) {
              console.log('🗑️ 清理过期缓存:', cacheName)
              return caches.delete(cacheName)
            }
          })
        )
      }),

      // 立即控制所有客户端
      self.clients.claim()
    ])
  )
})

// 🔄 监听消息，支持手动清理缓存
self.addEventListener('message', (event) => {
  if (event.data && event.data.type === 'CLEAR_CACHE') {
    console.log('📨 收到清理缓存指令')

    event.waitUntil(
      caches
        .keys()
        .then((cacheNames) => {
          return Promise.all(
            cacheNames.map((cacheName) => {
              console.log('🗑️ 清理缓存:', cacheName)
              return caches.delete(cacheName)
            })
          )
        })
        .then(() => {
          // 通知客户端缓存已清理
          event.ports[0]?.postMessage({ success: true })
        })
    )
  }

  if (event.data && event.data.type === 'SKIP_WAITING') {
    console.log('📨 收到跳过等待指令')
    self.skipWaiting()
  }
})

// 🚀 Workbox 后台同步配置
if (typeof workbox !== 'undefined' && workbox && workbox.backgroundSync) {
  try {
    console.log('✅ Workbox background sync available')
  } catch (error) {
    console.warn('⚠️ Background sync not available:', error)
  }
} else {
  console.log('⚠️ Workbox background sync not available')
}

// 🎯 降级处理和调试
if (!workboxLoaded) {
  console.log('⚠️ Workbox not available, using fallback fetch handler')

  self.addEventListener('fetch', (event) => {
    const { request } = event
    const url = new URL(request.url)

    // 跳过非 GET 请求和特殊协议
    if (request.method !== 'GET' || url.protocol === 'chrome-extension:') {
      return
    }

    // 记录所有 API 请求
    if (url.pathname.startsWith('/api/')) {
      console.log('🌐 Fallback: API request intercepted:', url.pathname)
    }

    // 简单的网络优先策略作为降级
    event.respondWith(
      fetch(request)
        .then((response) => {
          // 如果网络成功，尝试缓存响应
          if (response.ok && url.pathname.startsWith('/api/')) {
            console.log('💾 Fallback: Caching API response:', url.pathname)
            const responseClone = response.clone()
            caches.open('fallback-cache-' + VERSION).then((cache) => {
              cache.put(request, responseClone)
            })
          }
          return response
        })
        .catch(() => {
          // 网络失败，尝试从缓存返回
          console.log('🔄 Fallback: Network failed, trying cache:', url.pathname)
          return caches.match(request)
        })
    )
  })
} else {
  console.log('✅ Workbox is handling all requests')

  // 减少调试日志输出
  // self.addEventListener('fetch', (event) => {
  //   const url = new URL(event.request.url)
  //   if (url.pathname.startsWith('/api/')) {
  //     console.log('🔍 Debug: API request detected:', url.pathname)
  //   }
  // })
}

// 🚀 Workbox 推送通知支持
if (workbox) {
  // 配置推送通知
  console.log('✅ Workbox push notification support enabled')
}

// 🔄 后台同步事件处理
self.addEventListener('sync', (event) => {
  if (event.tag === 'api-queue') {
    console.log('🔄 Background sync triggered for API queue')
    event.waitUntil(
      // Workbox 会自动处理后台同步队列
      Promise.resolve()
    )
  }
})

// 推送通知
self.addEventListener('push', (event) => {
  if (event.data) {
    const data = event.data.json()
    const options = {
      body: data.body,
      icon: 'https://cdn.magiclight.ai/assets/playshot/playshot-icon.png',
      badge: 'https://cdn.magiclight.ai/assets/playshot/badge.png',
      vibrate: [100, 50, 100],
      data: {
        dateOfArrival: Date.now(),
        primaryKey: data.primaryKey
      },
      actions: [
        {
          action: 'explore',
          title: 'Explore',
          icon: 'https://cdn.magiclight.ai/assets/playshot/icon-explore.png'
        },
        {
          action: 'close',
          title: 'Close',
          icon: 'https://cdn.magiclight.ai/assets/playshot/icon-close.png'
        }
      ]
    }
    event.waitUntil(self.registration.showNotification(data.title, options))
  }
})

// 通知点击
self.addEventListener('notificationclick', (event) => {
  event.notification.close()

  if (event.action === 'explore') {
    event.waitUntil(clients.openWindow('/'))
  }
})

console.log('🎉 Service Worker fully optimized with Workbox strategies')
